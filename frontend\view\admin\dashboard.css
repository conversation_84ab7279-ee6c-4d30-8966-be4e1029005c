/* Styles pour le tableau de bord administrateur */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.5rem;
    color: #e74c3c;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 1rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

nav a:hover,
nav a.active {
    background-color: #34495e;
}

/* Boutons */
.btn-primary {
    background-color: #e74c3c;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: #c0392b;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-success {
    background-color: #27ae60;
    color: white;
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

/* Main content */
main {
    padding: 2rem 0;
}

.section {
    display: none;
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section.active {
    display: block;
}

.section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #e74c3c;
}

.stat-card h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.stat-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Section controls */
.section-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.section-controls input,
.section-controls select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

tr:hover {
    background-color: #f8f9fa;
}

/* Status badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-confirmed {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* Activity list */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-left: 3px solid #e74c3c;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.activity-time {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

/* Messages */
.message {
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    display: none;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Responsive */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        gap: 1rem;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}

/* Search container */
.search-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    max-width: 400px;
}

.search-container input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.search-container input:focus {
    outline: none;
    border-color: #e74c3c;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.search-container button {
    padding: 0.5rem;
    min-width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    border-radius: 4px;
}

.search-info {
    font-size: 0.85rem;
    color: #666;
    margin-left: auto;
    white-space: nowrap;
}

/* Search results highlighting */
.search-highlight {
    background-color: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 2px;
    font-weight: 500;
}

/* Enhanced section controls for search */
.section-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

/* Responsive search */
@media (max-width: 768px) {
    .search-container {
        max-width: 100%;
        order: 1;
        width: 100%;
    }

    .search-info {
        margin-left: 0;
        margin-top: 0.5rem;
        order: 2;
        width: 100%;
    }

    .section-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .section-controls > button {
        order: 0;
        align-self: flex-start;
    }
}

/* Delete table modal styles */
.warning-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
    color: #856404;
}

.warning-message strong {
    color: #d63031;
}

.table-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
}

.table-info p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.table-info strong {
    color: #495057;
    font-weight: 600;
}

#delete-table-content {
    margin-bottom: 1.5rem;
}

#delete-table-message {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #495057;
}

/* Enhanced modal for delete confirmation */
.modal .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    font-weight: 600;
}

.modal .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* Status badges for table status */
.status-badge.status-available {
    background-color: #28a745;
    color: white;
}

.status-badge.status-maintenance {
    background-color: #ffc107;
    color: #212529;
}

.status-badge.status-reserved {
    background-color: #dc3545;
    color: white;
}

/* Delete menu modal styles */
.menu-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
}

.menu-info p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.menu-info strong {
    color: #495057;
    font-weight: 600;
}

.dishes-preview {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
    max-height: 200px;
    overflow-y: auto;
}

.dishes-preview h4 {
    color: #856404;
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.dishes-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.dishes-list li {
    padding: 0.3rem 0;
    border-bottom: 1px solid #f0e68c;
    color: #856404;
    font-size: 0.9rem;
}

.dishes-list li:last-child {
    border-bottom: none;
}

.dishes-list strong {
    color: #d63031;
}

.dishes-list small {
    color: #6c757d;
    font-style: italic;
    display: block;
    margin-top: 0.2rem;
}

.dishes-stats {
    background-color: #e9ecef;
    border-radius: 4px;
    padding: 0.75rem;
    margin: 0.5rem 0;
    border-left: 4px solid #007bff;
}

.dishes-stats p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.dishes-stats strong {
    color: #495057;
}

.no-dishes-warning {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin: 0.5rem 0;
}

#delete-menu-content {
    margin-bottom: 1.5rem;
}

#delete-menu-message {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #495057;
}

/* Utilities */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
