<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Variable Names Compatibility</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .test-data {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test de Compatibilité des Noms de Variables</h1>
        <p>Cette page teste la compatibilité des noms de variables entre Frontend et Backend après les corrections.</p>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="total-tests">0</div>
                <div>Tests Total</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="passed-tests">0</div>
                <div>Réussis</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="failed-tests">0</div>
                <div>Échoués</div>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="runAllTests()" id="runAllBtn">🚀 Exécuter tous les tests</button>
            <button onclick="testUserFunctions()" id="testUserBtn">👤 Test User API</button>
            <button onclick="testReservationFunctions()" id="testReservationBtn">📅 Test Reservation API</button>
            <button onclick="clearResults()">🗑️ Effacer les résultats</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script src="model/User.js"></script>
    <script src="model/Reservation.js"></script>
    <script src="model/Table.js"></script>

    <script>
        let testResults = { total: 0, passed: 0, failed: 0 };

        function updateStats() {
            document.getElementById('total-tests').textContent = testResults.total;
            document.getElementById('passed-tests').textContent = testResults.passed;
            document.getElementById('failed-tests').textContent = testResults.failed;
        }

        function addResult(title, message, type, data = null) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-section';
            
            let dataHtml = '';
            if (data) {
                dataHtml = `<div class="test-data">Données envoyées: ${JSON.stringify(data, null, 2)}</div>`;
            }
            
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="test-result ${type}">
                    ${message}
                </div>
                ${dataHtml}
            `;
            
            resultsDiv.appendChild(resultDiv);
            
            testResults.total++;
            if (type === 'success') {
                testResults.passed++;
            } else if (type === 'error') {
                testResults.failed++;
            }
            
            updateStats();
        }

        async function testUserUpdateClient() {
            try {
                // Test avec des données factices
                const testData = {
                    client_id: 1,
                    name: "Test User",
                    phone: "0123456789"
                };

                // Simuler l'appel (ne pas vraiment envoyer)
                const mockResponse = await simulateAPICall('user.php?action=update_client', testData);
                
                if (mockResponse.hasCorrectFields) {
                    addResult(
                        '✅ Test User.updateClientInfo()',
                        'Les noms de variables sont corrects: client_id, name, phone',
                        'success',
                        testData
                    );
                } else {
                    addResult(
                        '❌ Test User.updateClientInfo()',
                        'Erreur dans les noms de variables',
                        'error',
                        testData
                    );
                }
            } catch (error) {
                addResult(
                    '❌ Test User.updateClientInfo()',
                    `Erreur: ${error.message}`,
                    'error'
                );
            }
        }

        async function testUserChangePassword() {
            try {
                const testData = {
                    user_id: 1,
                    current_password: "oldpass123",
                    new_password: "newpass123"
                };

                const mockResponse = await simulateAPICall('user.php?action=change_password', testData);
                
                if (mockResponse.hasCorrectFields) {
                    addResult(
                        '✅ Test User.changePassword()',
                        'Les noms de variables sont corrects: user_id, current_password, new_password',
                        'success',
                        testData
                    );
                } else {
                    addResult(
                        '❌ Test User.changePassword()',
                        'Erreur dans les noms de variables',
                        'error',
                        testData
                    );
                }
            } catch (error) {
                addResult(
                    '❌ Test User.changePassword()',
                    `Erreur: ${error.message}`,
                    'error'
                );
            }
        }

        async function testReservationCreate() {
            try {
                const testData = {
                    client_id: 1,
                    table_id: 2,
                    date: "2024-01-15",
                    time: "19:00"
                };

                const mockResponse = await simulateAPICall('reservation.php?action=create_reservation', testData);
                
                if (mockResponse.hasCorrectFields) {
                    addResult(
                        '✅ Test Reservation.createReservation()',
                        'Les noms de variables sont corrects: client_id, table_id, date, time',
                        'success',
                        testData
                    );
                } else {
                    addResult(
                        '❌ Test Reservation.createReservation()',
                        'Erreur dans les noms de variables',
                        'error',
                        testData
                    );
                }
            } catch (error) {
                addResult(
                    '❌ Test Reservation.createReservation()',
                    `Erreur: ${error.message}`,
                    'error'
                );
            }
        }

        async function testReservationGetClient() {
            try {
                const testData = {
                    client_id: 1
                };

                const mockResponse = await simulateAPICall('reservation.php?action=get_client_reservations', testData);
                
                if (mockResponse.hasCorrectFields) {
                    addResult(
                        '✅ Test Reservation.getClientReservations()',
                        'Les noms de variables sont corrects: client_id',
                        'success',
                        testData
                    );
                } else {
                    addResult(
                        '❌ Test Reservation.getClientReservations()',
                        'Erreur dans les noms de variables',
                        'error',
                        testData
                    );
                }
            } catch (error) {
                addResult(
                    '❌ Test Reservation.getClientReservations()',
                    `Erreur: ${error.message}`,
                    'error'
                );
            }
        }

        // Simulation d'appel API pour vérifier les noms de variables
        async function simulateAPICall(endpoint, data) {
            // Vérifier les champs attendus selon l'endpoint
            const expectedFields = {
                'user.php?action=update_client': ['client_id', 'name', 'phone'],
                'user.php?action=change_password': ['user_id', 'current_password', 'new_password'],
                'reservation.php?action=create_reservation': ['client_id', 'table_id', 'date', 'time'],
                'reservation.php?action=get_client_reservations': ['client_id']
            };

            const expected = expectedFields[endpoint];
            if (!expected) {
                throw new Error(`Endpoint non reconnu: ${endpoint}`);
            }

            const hasCorrectFields = expected.every(field => data.hasOwnProperty(field));
            
            return {
                hasCorrectFields,
                expectedFields: expected,
                actualFields: Object.keys(data)
            };
        }

        async function testUserFunctions() {
            document.getElementById('testUserBtn').disabled = true;
            await testUserUpdateClient();
            await testUserChangePassword();
            document.getElementById('testUserBtn').disabled = false;
        }

        async function testReservationFunctions() {
            document.getElementById('testReservationBtn').disabled = true;
            await testReservationCreate();
            await testReservationGetClient();
            document.getElementById('testReservationBtn').disabled = false;
        }

        async function runAllTests() {
            document.getElementById('runAllBtn').disabled = true;
            clearResults();
            
            addResult(
                '🚀 Début des tests',
                'Exécution de tous les tests de compatibilité...',
                'warning'
            );

            await testUserFunctions();
            await testReservationFunctions();
            
            addResult(
                '🏁 Tests terminés',
                `Résultats: ${testResults.passed}/${testResults.total} tests réussis`,
                testResults.failed === 0 ? 'success' : 'warning'
            );
            
            document.getElementById('runAllBtn').disabled = false;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }

        // Initialisation
        updateStats();
    </script>
</body>
</html>
