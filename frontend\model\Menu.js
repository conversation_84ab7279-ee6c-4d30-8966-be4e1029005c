/**
 * Mod<PERSON>le pour gérer les opérations liées aux menus et plats
 */
class Menu {
    // Méthode pour obtenir tous les menus avec leurs plats
    static async getAllMenus() {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=getAllMenus');
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des menus:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir un menu par son ID
    static async getMenuById(menuId) {
        try {
            const response = await fetch(`http://restaurantmila.atwebpages.com/sever/menu.php?action=getMenuById&id=${menuId}`);
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération du menu:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour obtenir tous les plats
    static async getAllDishes() {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=getAllDishes');
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la récupération des plats:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour ajouter un nouveau menu
    static async addMenu(name) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=addMenu', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de l\'ajout du menu:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour mettre à jour un menu
    static async updateMenu(menuId, name) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=updateMenu', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ menu_id: menuId, name })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la mise à jour du menu:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour supprimer un menu
    static async deleteMenu(menuId) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=deleteMenu', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ menu_id: menuId })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la suppression du menu:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour ajouter un nouveau plat
    static async addDish(name, description, price, menuId) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=addDish', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, description, price, menu_id: menuId })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de l\'ajout du plat:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour mettre à jour un plat
    static async updateDish(dishId, name, description, price, menuId) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=updateDish', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ dish_id: dishId, name, description, price, menu_id: menuId })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la mise à jour du plat:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }

    // Méthode pour supprimer un plat
    static async deleteDish(dishId) {
        try {
            const response = await fetch('http://restaurantmila.atwebpages.com/sever/menu.php?action=deleteDish', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ dish_id: dishId })
            });
            return await response.json();
        } catch (error) {
            console.error('Erreur lors de la suppression du plat:', error);
            return { success: false, message: 'Erreur de connexion au serveur' };
        }
    }
}
