/* Styles spécifiques pour la page menu */

.header {
    background: linear-gradient(135deg, #2c5530, #4a7c59);
    color: white;
    padding: 2rem 0;
    text-align: center;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-content p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.nav-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.nav-buttons .btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.menu-header {
    text-align: center;
    margin-bottom: 3rem;
}

.menu-header h2 {
    color: #2c5530;
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
}

.menu-header p {
    color: #666;
    font-size: 1.1rem;
    font-style: italic;
}

.menus-container {
    display: grid;
    gap: 3rem;
}

.menu-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.menu-section:hover {
    transform: translateY(-5px);
}

.menu-title {
    background: linear-gradient(135deg, #4a7c59, #2c5530);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.menu-title h3 {
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.dishes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.dish-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #4a7c59;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dish-card:hover {
    background: #e8f5e8;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(74, 124, 89, 0.2);
}

.dish-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #4a7c59, #2c5530);
    transition: width 0.3s ease;
}

.dish-card:hover::before {
    width: 8px;
}

.dish-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c5530;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dish-name::before {
    content: '🍽️';
    font-size: 1.1rem;
}

.dish-description {
    color: #555;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-style: italic;
}

.dish-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #d4a574;
    text-align: right;
    padding: 0.5rem;
    background: rgba(212, 165, 116, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(212, 165, 116, 0.3);
}

.dish-price::before {
    content: '💰 ';
}

.loading {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4a7c59;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    text-align: center;
    padding: 3rem;
    background: #f8d7da;
    border-radius: 10px;
    margin: 2rem 0;
    border: 1px solid #f5c6cb;
}

.error-message h3 {
    color: #721c24;
    margin-bottom: 1rem;
}

.error-message p {
    color: #721c24;
    margin-bottom: 1.5rem;
}

.hidden {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content h1 {
        font-size: 2rem;
    }
    
    .nav-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .nav-buttons .btn {
        width: 200px;
    }
    
    .dishes-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .menu-header h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 1.5rem 0;
    }
    
    .header-content h1 {
        font-size: 1.8rem;
    }
    
    .header-content p {
        font-size: 1rem;
    }
    
    .dish-card {
        padding: 1rem;
    }
    
    .dish-name {
        font-size: 1.1rem;
    }
}
