# دليل النشر - Restaurant Reservation System

## خطوات النشر على الخادم

### 1. تحضير الملفات

#### ملفات الواجهة الخلفية (Backend)
قم برفع الملفات التالية إلى `http://restaurantmila.atwebpages.com/sever/`:

```
backend/server/
├── database.php
├── user.php
├── reservation.php
├── table.php
├── menu.php
└── .htaccess
```

#### ملفات قاعدة البيانات
```
backend/
├── database.sql
└── test_connection.php
```

### 2. إعداد قاعدة البيانات

1. **إنشاء قاعدة البيانات:**
   - اسم قاعدة البيانات: `4612192_restaurant`
   - استيراد ملف `database.sql`

2. **تحديث إعدادات الاتصال:**
   ```php
   // في ملف database.php
   $host = 'localhost'; // أو عنوان الخادم المحدد
   $dbname = '4612192_restaurant';
   $username = 'اسم_المستخدم_الخاص_بك';
   $password = 'كلمة_المرور_الخاصة_بك';
   ```

### 3. رفع ملفات الواجهة الأمامية

يمكن رفع ملفات الواجهة الأمامية إلى أي خادم ويب أو استضافة:

```
frontend/
├── index.html
├── style.css
├── script.js
├── view/
│   ├── login/
│   ├── signup/
│   ├── reservation/
│   ├── admin/
│   ├── manager/
│   └── client/
├── model/
├── controller/
└── test.html
```

### 4. اختبار النظام

1. **افتح صفحة الاختبار:**
   ```
   http://your-frontend-domain/test.html
   ```

2. **قم بتشغيل جميع الاختبارات:**
   - اختبار الاتصال بقاعدة البيانات
   - اختبار APIs المستخدمين
   - اختبار APIs الحجوزات
   - اختبار APIs الطاولات

### 5. إدراج البيانات التجريبية

استخدم ملف `test_connection.php` لإدراج البيانات التجريبية:

```
http://restaurantmila.atwebpages.com/sever/test_connection.php?action=insert_data
```

### 6. حسابات الاختبار

بعد إدراج البيانات التجريبية، يمكنك استخدام الحسابات التالية:

| الدور | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| مسؤول | <EMAIL> | password123 |
| مدير | <EMAIL> | password123 |
| عميل | <EMAIL> | password123 |
| عميل | <EMAIL> | password123 |

## التحقق من التكوين

### 1. فحص CORS
تأكد من أن headers CORS تعمل بشكل صحيح:
```bash
curl -H "Origin: http://your-frontend-domain" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     http://restaurantmila.atwebpages.com/sever/user.php
```

### 2. فحص APIs
اختبر API المستخدمين:
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"test123"}' \
     http://restaurantmila.atwebpages.com/sever/user.php?action=login
```

### 3. فحص قاعدة البيانات
```bash
curl http://restaurantmila.atwebpages.com/sever/test_connection.php?action=test_connection
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ CORS:**
   - تأكد من وجود ملف `.htaccess`
   - تحقق من headers في ملفات PHP

2. **خطأ قاعدة البيانات:**
   - تحقق من إعدادات الاتصال في `database.php`
   - تأكد من استيراد `database.sql` بشكل صحيح

3. **خطأ 404:**
   - تحقق من مسارات الملفات
   - تأكد من رفع جميع الملفات المطلوبة

4. **خطأ صلاحيات الملفات:**
   ```bash
   chmod 755 /path/to/sever/
   chmod 644 /path/to/sever/*.php
   ```

## الأمان

### إعدادات الأمان المطلوبة

1. **تغيير كلمات المرور الافتراضية:**
   - قم بتغيير كلمات مرور الحسابات التجريبية
   - استخدم كلمات مرور قوية

2. **تحديث إعدادات قاعدة البيانات:**
   - استخدم مستخدم قاعدة بيانات مخصص
   - قم بتقييد الصلاحيات

3. **حماية الملفات الحساسة:**
   - تأكد من حماية `database.php`
   - استخدم HTTPS في الإنتاج

## المراقبة والصيانة

### مراقبة النظام

1. **فحص السجلات:**
   - راقب سجلات الخادم للأخطاء
   - تتبع استخدام قاعدة البيانات

2. **النسخ الاحتياطية:**
   - قم بعمل نسخ احتياطية يومية لقاعدة البيانات
   - احتفظ بنسخ من ملفات التطبيق

3. **التحديثات:**
   - راقب تحديثات PHP وMySQL
   - اختبر التطبيق بعد أي تحديثات

## الدعم الفني

### معلومات مفيدة للدعم

- **إصدار PHP:** 7.4+
- **إصدار MySQL:** 5.7+
- **المتطلبات:** PDO, PDO_MySQL, session
- **الخادم:** Apache مع mod_rewrite

### ملفات السجلات

راجع السجلات التالية عند حدوث مشاكل:
- سجلات خطأ Apache
- سجلات خطأ PHP
- سجلات قاعدة البيانات

---

**ملاحظة:** هذا الدليل مخصص للنشر على خادم `restaurantmila.atwebpages.com`. قم بتعديل المسارات والإعدادات حسب بيئة الخادم الخاصة بك.
