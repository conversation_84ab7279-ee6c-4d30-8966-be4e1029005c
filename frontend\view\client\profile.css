/* Styles pour le profil client */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.5rem;
    color: #e74c3c;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 1rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

nav a:hover,
nav a.active {
    background-color: #34495e;
}

/* Boutons */
.btn-primary {
    background-color: #e74c3c;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
    font-size: 1rem;
}

.btn-primary:hover {
    background-color: #c0392b;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
    font-size: 1rem;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

/* Main content */
main {
    padding: 2rem 0;
}

.profile-container {
    max-width: 800px;
    margin: 0 auto;
}

.profile-container h1 {
    color: #2c3e50;
    margin-bottom: 2rem;
    text-align: center;
    font-size: 2.5rem;
}

/* Profile sections */
.profile-section {
    background-color: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.profile-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 0.5rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #e74c3c;
}

.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-group small {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}

/* Messages */
.message {
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
    display: none;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Reservations */
.reservations-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.reservations-filter select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.reservations-list {
    max-height: 400px;
    overflow-y: auto;
}

.reservation-item {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: box-shadow 0.3s;
}

.reservation-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.reservation-date {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.reservation-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-confirmed {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.reservation-details {
    margin-bottom: 1rem;
    color: #666;
}

.reservation-actions {
    display: flex;
    gap: 0.5rem;
}

.profile-actions {
    text-align: center;
    margin-top: 2rem;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* Responsive */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .profile-container h1 {
        font-size: 2rem;
    }
    
    .profile-section {
        padding: 1.5rem;
    }
    
    .reservation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .reservation-actions {
        flex-direction: column;
    }
    
    .reservations-filter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .reservation-actions {
        gap: 0.25rem;
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-danger,
    .btn-warning {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Utilities */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
