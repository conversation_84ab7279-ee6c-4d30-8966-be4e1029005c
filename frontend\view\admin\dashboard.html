<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Administrateur - Système de Réservation</title>
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Restaurant Gourmet - Admin</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="#dashboard" class="active">Tableau de bord</a></li>
                    <li><a href="#users">Utilisateurs</a></li>
                    <li><a href="#reservations">Réservations</a></li>
                    <li><a href="#tables">Tables</a></li>
                    <li><a href="#menu">Menu</a></li>
                    <li><a href="../../index.html">Accueil</a></li>
                    <li><a href="#" id="logout-btn" class="btn-primary">Déconnexion</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <!-- Section Tableau de bord -->
        <section id="dashboard" class="section active">
            <h2>Tableau de bord Administrateur</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Utilisateurs</h3>
                    <div class="stat-number" id="total-users">0</div>
                    <p>Total des utilisateurs</p>
                </div>
                <div class="stat-card">
                    <h3>Réservations</h3>
                    <div class="stat-number" id="total-reservations">0</div>
                    <p>Réservations aujourd'hui</p>
                </div>
                <div class="stat-card">
                    <h3>Tables</h3>
                    <div class="stat-number" id="total-tables">0</div>
                    <p>Tables disponibles</p>
                </div>
                <div class="stat-card">
                    <h3>Revenus</h3>
                    <div class="stat-number" id="total-revenue">0€</div>
                    <p>Revenus du mois</p>
                </div>
            </div>

            <div class="recent-activity">
                <h3>Activité récente</h3>
                <div id="recent-activity-list" class="activity-list">
                    <!-- Les activités récentes seront chargées ici -->
                </div>
            </div>
        </section>

        <!-- Section Gestion des utilisateurs -->
        <section id="users" class="section">
            <h2>Gestion des utilisateurs</h2>
            <div class="section-controls">
                <button class="btn-primary" onclick="refreshUsers()">Actualiser</button>
                <input type="text" id="user-search" placeholder="Rechercher un utilisateur..." onkeyup="filterUsers()">
            </div>

            <div class="table-container">
                <table id="users-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Email</th>
                            <th>Nom</th>
                            <th>Rôle</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-tbody">
                        <!-- Les utilisateurs seront chargés ici -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section Gestion des réservations -->
        <section id="reservations" class="section">
            <h2>Gestion des réservations</h2>
            <div class="section-controls">
                <button class="btn-primary" onclick="refreshReservations()">Actualiser</button>
                <input type="date" id="reservation-date" onchange="filterReservationsByDate()">
                <select id="reservation-status" onchange="filterReservationsByStatus()">
                    <option value="">Tous les statuts</option>
                    <option value="Pending">En attente</option>
                    <option value="Confirmed">Confirmée</option>
                    <option value="Cancelled">Annulée</option>
                </select>
            </div>

            <div class="table-container">
                <table id="reservations-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Client</th>
                            <th>Date/Heure</th>
                            <th>Table</th>
                            <th>Places</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="reservations-tbody">
                        <!-- Les réservations seront chargées ici -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section Gestion des tables -->
        <section id="tables" class="section">
            <h2>Gestion des tables</h2>
            <div class="section-controls">
                <button class="btn-primary" onclick="showAddTableModal()">Ajouter une table</button>
                <button class="btn-secondary" onclick="refreshTables()">Actualiser</button>
            </div>

            <div class="table-container">
                <table id="tables-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre de places</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="tables-tbody">
                        <!-- Les tables seront chargées ici -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Section Gestion du menu -->
        <section id="menu" class="section">
            <h2>Gestion du menu</h2>
            <div class="section-controls">
                <button class="btn-primary" onclick="showAddMenuModal()">Ajouter un menu</button>
                <button class="btn-primary" onclick="showAddDishModal()">Ajouter un plat</button>
                <button class="btn-secondary" onclick="refreshMenu()">Actualiser</button>
            </div>

            <div id="menu-content">
                <!-- Le contenu du menu sera chargé ici -->
            </div>
        </section>
    </main>

    <!-- Modal pour ajouter/modifier une table -->
    <div id="table-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('table-modal')">&times;</span>
            <h2 id="table-modal-title">Ajouter une table</h2>
            <form id="table-form">
                <input type="hidden" id="table-id">
                <div class="form-group">
                    <label for="table-seats">Nombre de places:</label>
                    <input type="number" id="table-seats" min="1" max="20" required>
                </div>
                <div class="form-group">
                    <label for="table-status">Statut:</label>
                    <select id="table-status" required>
                        <option value="Available">Disponible</option>
                        <option value="Maintenance">Maintenance</option>
                        <option value="Reserved">Réservée</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Enregistrer</button>
                    <button type="button" class="btn-secondary" onclick="closeModal('table-modal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal pour modifier le rôle d'un utilisateur -->
    <div id="user-role-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('user-role-modal')">&times;</span>
            <h2>Modifier le rôle de l'utilisateur</h2>
            <form id="user-role-form">
                <input type="hidden" id="user-role-id">
                <div class="form-group">
                    <label for="user-role-select">Nouveau rôle:</label>
                    <select id="user-role-select" required>
                        <option value="Client">Client</option>
                        <option value="Manager">Manager</option>
                        <option value="Admin">Administrateur</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Modifier</button>
                    <button type="button" class="btn-secondary" onclick="closeModal('user-role-modal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal pour ajouter/modifier un menu -->
    <div id="menu-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('menu-modal')">&times;</span>
            <h2 id="menu-modal-title">Ajouter un Menu</h2>
            <form id="menu-form" onsubmit="saveMenu(event)">
                <input type="hidden" id="menu-id">
                <div class="form-group">
                    <label for="menu-name">Nom du menu:</label>
                    <input type="text" id="menu-name" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Enregistrer</button>
                    <button type="button" class="btn-secondary" onclick="closeModal('menu-modal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal pour ajouter/modifier un plat -->
    <div id="dish-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('dish-modal')">&times;</span>
            <h2 id="dish-modal-title">Ajouter un Plat</h2>
            <form id="dish-form" onsubmit="saveDish(event)">
                <input type="hidden" id="dish-id">
                <input type="hidden" id="dish-menu-id">
                <div class="form-group">
                    <label for="dish-name">Nom du plat:</label>
                    <input type="text" id="dish-name" required>
                </div>
                <div class="form-group">
                    <label for="dish-description">Description:</label>
                    <textarea id="dish-description" required></textarea>
                </div>
                <div class="form-group">
                    <label for="dish-price">Prix (DA):</label>
                    <input type="number" id="dish-price" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Enregistrer</button>
                    <button type="button" class="btn-secondary" onclick="closeModal('dish-modal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <div id="message" class="message"></div>

    <!-- Scripts -->
    <script src="../../model/User.js"></script>
    <script src="../../model/Table.js"></script>
    <script src="../../model/Reservation.js"></script>
    <script src="../../model/Menu.js"></script>
    <script src="../../controller/userController.js"></script>
    <script src="../../controller/reservationController.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
