<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Administrateur - Système de Réservation</title>
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Restaurant Gourmet - Admin</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="#dashboard" class="active">Tableau de bord</a></li>
                    <li><a href="#users">Utilisateurs</a></li>
                    <li><a href="../../index.html">Accueil</a></li>
                    <li><a href="#" id="logout-btn" class="btn-primary">Déconnexion</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <!-- Section Tableau de bord -->
        <section id="dashboard" class="section active">
            <h2>Tableau de bord Administrateur</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Utilisateurs Totaux</h3>
                    <div class="stat-number" id="total-users">0</div>
                    <p>Total des utilisateurs</p>
                </div>
                <div class="stat-card">
                    <h3>Administrateurs</h3>
                    <div class="stat-number" id="total-admins">0</div>
                    <p>Nombre d'administrateurs</p>
                </div>
                <div class="stat-card">
                    <h3>Managers</h3>
                    <div class="stat-number" id="total-managers">0</div>
                    <p>Nombre de managers</p>
                </div>
                <div class="stat-card">
                    <h3>Clients</h3>
                    <div class="stat-number" id="total-clients">0</div>
                    <p>Nombre de clients</p>
                </div>
            </div>

            <div class="recent-activity">
                <h3>Activité récente</h3>
                <div id="recent-activity-list" class="activity-list">
                    <!-- Les activités récentes seront chargées ici -->
                </div>
            </div>
        </section>

        <!-- Section Gestion des utilisateurs -->
        <section id="users" class="section">
            <h2>Gestion des utilisateurs</h2>
            <div class="section-controls">
                <button class="btn-primary" onclick="refreshUsers()">Actualiser</button>
                <div class="search-container">
                    <input type="text" id="user-search" placeholder="Rechercher par nom, email, rôle ou ID..." onkeyup="handleSearchKeyup(event)" oninput="filterUsers()" autocomplete="off">
                    <button class="btn-secondary" onclick="clearSearch()" title="Effacer la recherche">✕</button>
                </div>
                <div class="search-info">
                    <span id="search-results-count"></span>
                </div>
            </div>

            <div class="table-container">
                <table id="users-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Email</th>
                            <th>Nom</th>
                            <th>Rôle</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-tbody">
                        <!-- Les utilisateurs seront chargés ici -->
                    </tbody>
                </table>
            </div>
        </section>


    </main>



    <!-- Modal pour modifier le rôle d'un utilisateur -->
    <div id="user-role-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('user-role-modal')">&times;</span>
            <h2>Modifier le rôle de l'utilisateur</h2>
            <form id="user-role-form">
                <input type="hidden" id="user-role-id">
                <div class="form-group">
                    <label for="user-role-select">Nouveau rôle:</label>
                    <select id="user-role-select" required>
                        <option value="Client">Client</option>
                        <option value="Manager">Manager</option>
                        <option value="Admin">Administrateur</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Modifier</button>
                    <button type="button" class="btn-secondary" onclick="closeModal('user-role-modal')">Annuler</button>
                </div>
            </form>
        </div>
    </div>



    <div id="message" class="message"></div>

    <!-- Scripts -->
    <script src="../../model/User.js"></script>
    <script src="../../controller/userController.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>
