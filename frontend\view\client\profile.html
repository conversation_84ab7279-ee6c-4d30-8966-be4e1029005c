<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Profil - Système de Réservation</title>
    <link rel="stylesheet" href="profile.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1>Restaurant Gourmet</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Accueil</a></li>
                    <li><a href="../reservation/reservation.html">Réservations</a></li>
                    <li><a href="#profile" class="active">Mon Profil</a></li>
                    <li><a href="#" id="logout-btn" class="btn-primary">Déconnexion</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="profile-container">
            <h1>Mon Profil</h1>
            
            <!-- Section informations personnelles -->
            <section class="profile-section">
                <h2>Informations personnelles</h2>
                <form id="profile-form">
                    <div class="form-group">
                        <label for="name">Nom complet:</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" readonly>
                        <small>L'email ne peut pas être modifié</small>
                    </div>
                    <div class="form-group">
                        <label for="phone">Téléphone:</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn-primary">Mettre à jour</button>
                    </div>
                </form>
                <div id="profile-message" class="message"></div>
            </section>

            <!-- Section changement de mot de passe -->
            <section class="profile-section">
                <h2>Changer le mot de passe</h2>
                <form id="password-form">
                    <div class="form-group">
                        <label for="current-password">Mot de passe actuel:</label>
                        <input type="password" id="current-password" name="current-password" required>
                    </div>
                    <div class="form-group">
                        <label for="new-password">Nouveau mot de passe:</label>
                        <input type="password" id="new-password" name="new-password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm-password">Confirmer le nouveau mot de passe:</label>
                        <input type="password" id="confirm-password" name="confirm-password" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn-primary">Changer le mot de passe</button>
                    </div>
                </form>
                <div id="password-message" class="message"></div>
            </section>

            <!-- Section historique des réservations -->
            <section class="profile-section">
                <h2>Mes réservations</h2>
                <div class="reservations-filter">
                    <select id="status-filter" onchange="filterReservations()">
                        <option value="">Toutes les réservations</option>
                        <option value="Confirmed">Confirmées</option>
                        <option value="Pending">En attente</option>
                        <option value="Cancelled">Annulées</option>
                    </select>
                    <button class="btn-secondary" onclick="loadReservations()">Actualiser</button>
                </div>
                
                <div id="reservations-list" class="reservations-list">
                    <!-- Les réservations seront chargées ici -->
                </div>
                
                <div class="profile-actions">
                    <a href="../reservation/reservation.html" class="btn-primary">Nouvelle réservation</a>
                </div>
            </section>

            <!-- Section statistiques personnelles -->
            <section class="profile-section">
                <h2>Mes statistiques</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="total-reservations">0</div>
                        <div class="stat-label">Réservations totales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="upcoming-reservations">0</div>
                        <div class="stat-label">Réservations à venir</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="favorite-table">-</div>
                        <div class="stat-label">Table préférée</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="member-since">-</div>
                        <div class="stat-label">Membre depuis</div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Modal pour modifier une réservation -->
    <div id="edit-reservation-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Modifier la réservation</h2>
            <form id="edit-reservation-form">
                <input type="hidden" id="edit-reservation-id">
                <div class="form-group">
                    <label for="edit-date">Date:</label>
                    <input type="date" id="edit-date" required>
                </div>
                <div class="form-group">
                    <label for="edit-time">Heure:</label>
                    <select id="edit-time" required>
                        <option value="12:00">12:00</option>
                        <option value="12:30">12:30</option>
                        <option value="13:00">13:00</option>
                        <option value="13:30">13:30</option>
                        <option value="19:00">19:00</option>
                        <option value="19:30">19:30</option>
                        <option value="20:00">20:00</option>
                        <option value="20:30">20:30</option>
                        <option value="21:00">21:00</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-table">Table:</label>
                    <select id="edit-table" required>
                        <!-- Les options seront chargées dynamiquement -->
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Enregistrer</button>
                    <button type="button" class="btn-secondary" onclick="closeModal()">Annuler</button>
                </div>
            </form>
            <div id="edit-message" class="message"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../model/User.js"></script>
    <script src="../../model/Reservation.js"></script>
    <script src="../../controller/userController.js"></script>
    <script src="../../controller/reservationController.js"></script>
    <script src="profile.js"></script>
</body>
</html>
