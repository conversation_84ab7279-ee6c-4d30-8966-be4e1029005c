<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug User Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .user-data {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        pre {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug User Data</h1>
        <p>Cette page permet de vérifier les données utilisateur stockées dans localStorage.</p>
        
        <div class="controls">
            <button onclick="checkUserData()">🔄 Vérifier les données</button>
            <button onclick="testLogin()" id="testLoginBtn">🧪 Test Login</button>
            <button onclick="clearUserData()" class="clear-btn">🗑️ Effacer les données</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function checkUserData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            try {
                const userDataString = localStorage.getItem('user');
                
                if (!userDataString) {
                    resultsDiv.innerHTML = `
                        <div class="user-data error">
                            <h3>❌ Aucune donnée utilisateur trouvée</h3>
                            <p>L'utilisateur n'est pas connecté ou les données ont été supprimées.</p>
                        </div>
                    `;
                    return;
                }
                
                const userData = JSON.parse(userDataString);
                
                resultsDiv.innerHTML = `
                    <div class="user-data success">
                        <h3>✅ Données utilisateur trouvées</h3>
                        <p><strong>Données brutes:</strong></p>
                        <pre>${JSON.stringify(userData, null, 2)}</pre>
                    </div>
                    
                    <div class="user-data">
                        <h3>🔍 Analyse des champs</h3>
                        <p><strong>ID:</strong> ${userData.id || 'Non défini'}</p>
                        <p><strong>Rôle:</strong> ${userData.role || 'Non défini'}</p>
                        <p><strong>Client ID:</strong> ${userData.client_id || 'Non défini'}</p>
                        <p><strong>Client ID (alt):</strong> ${userData.clientId || 'Non défini'}</p>
                        <p><strong>Nom:</strong> ${userData.name || 'Non défini'}</p>
                        <p><strong>Email:</strong> ${userData.email || 'Non défini'}</p>
                    </div>
                    
                    <div class="user-data ${userData.client_id || userData.clientId ? 'success' : 'error'}">
                        <h3>${userData.client_id || userData.clientId ? '✅' : '❌'} Statut Client ID</h3>
                        <p>
                            ${userData.client_id || userData.clientId 
                                ? `Client ID disponible: ${userData.client_id || userData.clientId}` 
                                : 'Client ID manquant - cela causera des problèmes avec les réservations'}
                        </p>
                    </div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="user-data error">
                        <h3>❌ Erreur lors de la lecture des données</h3>
                        <p>Erreur: ${error.message}</p>
                        <p>Les données utilisateur pourraient être corrompues.</p>
                    </div>
                `;
            }
        }
        
        async function testLogin() {
            const resultsDiv = document.getElementById('results');
            const testLoginBtn = document.getElementById('testLoginBtn');
            
            testLoginBtn.disabled = true;
            testLoginBtn.textContent = '⏳ Test en cours...';
            
            try {
                const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    resultsDiv.innerHTML = `
                        <div class="user-data success">
                            <h3>✅ Test de connexion réussi</h3>
                            <p>Données reçues du serveur:</p>
                            <pre>${JSON.stringify(data.user, null, 2)}</pre>
                            <p>Les données ont été sauvegardées dans localStorage.</p>
                        </div>
                    `;
                    
                    // Vérifier automatiquement les données après le test
                    setTimeout(checkUserData, 1000);
                } else {
                    resultsDiv.innerHTML = `
                        <div class="user-data error">
                            <h3>❌ Échec du test de connexion</h3>
                            <p>Erreur: ${data.message}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="user-data error">
                        <h3>❌ Erreur de connexion au serveur</h3>
                        <p>Erreur: ${error.message}</p>
                        <p>Vérifiez que le serveur est accessible.</p>
                    </div>
                `;
            } finally {
                testLoginBtn.disabled = false;
                testLoginBtn.textContent = '🧪 Test Login';
            }
        }
        
        function clearUserData() {
            if (confirm('Êtes-vous sûr de vouloir effacer les données utilisateur?')) {
                localStorage.removeItem('user');
                checkUserData();
            }
        }
        
        // Vérifier automatiquement au chargement de la page
        window.addEventListener('DOMContentLoaded', checkUserData);
    </script>
</body>
</html>
