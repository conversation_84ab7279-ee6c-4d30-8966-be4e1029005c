<?php
// Script pour insérer les données du menu algérien

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'server/database.php';

function insertAlgerianMenu() {
    try {
        $conn = connectDB();
        
        // Vérifier si les menus existent déjà
        $stmt = $conn->prepare("SELECT COUNT(*) FROM Menu");
        $stmt->execute();
        $menuCount = $stmt->fetchColumn();
        
        if ($menuCount > 0) {
            return ["success" => false, "message" => "Les menus existent déjà dans la base de données."];
        }
        
        // Commencer une transaction
        $conn->beginTransaction();
        
        // Données des menus algériens
        $menusData = [
            [
                'name' => 'Entrées Traditionnelles',
                'dishes' => [
                    [
                        'name' => 'Chorba Frik',
                        'description' => 'Soupe traditionnelle algérienne aux légumes et blé concassé, parfumée aux épices et à la coriandre fraîche',
                        'price' => 450
                    ],
                    [
                        'name' => 'Bourek aux Épinards',
                        'description' => 'Feuilletés croustillants farcis aux épinards, ricotta et fines herbes, servis chauds',
                        'price' => 350
                    ],
                    [
                        'name' => 'Salade Mechouia',
                        'description' => 'Salade de poivrons et tomates grillés, assaisonnée à l\'huile d\'olive et aux câpres',
                        'price' => 300
                    ],
                    [
                        'name' => 'Brik à l\'Œuf',
                        'description' => 'Pâte fine croustillante farcie d\'un œuf, thon, câpres et persil, frite à la perfection',
                        'price' => 400
                    ]
                ]
            ],
            [
                'name' => 'Plats Principaux',
                'dishes' => [
                    [
                        'name' => 'Couscous Royal',
                        'description' => 'Couscous traditionnel aux sept légumes, agneau, merguez et poulet, sauce rouge parfumée',
                        'price' => 1200
                    ],
                    [
                        'name' => 'Tajine Zitoune',
                        'description' => 'Tajine d\'agneau aux olives vertes, citron confit et gingembre, mijoté lentement',
                        'price' => 1100
                    ],
                    [
                        'name' => 'Rechta aux Légumes',
                        'description' => 'Pâtes fraîches algériennes dans un bouillon riche aux légumes de saison et épices',
                        'price' => 800
                    ],
                    [
                        'name' => 'Mechoui d\'Agneau',
                        'description' => 'Épaule d\'agneau rôtie aux herbes de Provence et épices berbères, accompagnée de légumes grillés',
                        'price' => 1500
                    ],
                    [
                        'name' => 'Dolma aux Légumes',
                        'description' => 'Légumes farcis (courgettes, aubergines, poivrons) au riz parfumé et viande hachée',
                        'price' => 950
                    ],
                    [
                        'name' => 'Chakhchoukha',
                        'description' => 'Plat traditionnel de galettes de pain émincées dans une sauce rouge aux légumes et viande',
                        'price' => 750
                    ]
                ]
            ],
            [
                'name' => 'Grillades et Spécialités',
                'dishes' => [
                    [
                        'name' => 'Merguez Grillées',
                        'description' => 'Saucisses épicées grillées au feu de bois, servies avec pain traditionnel et harissa',
                        'price' => 650
                    ],
                    [
                        'name' => 'Kefta aux Herbes',
                        'description' => 'Boulettes de viande hachée aux herbes fraîches, grillées et servies avec sauce tomate',
                        'price' => 700
                    ],
                    [
                        'name' => 'Poisson Grillé Chermoula',
                        'description' => 'Poisson frais mariné dans la chermoula (coriandre, persil, ail, épices) et grillé',
                        'price' => 1000
                    ],
                    [
                        'name' => 'Brochettes d\'Agneau',
                        'description' => 'Cubes d\'agneau marinés aux épices berbères, grillés avec légumes colorés',
                        'price' => 1200
                    ]
                ]
            ],
            [
                'name' => 'Desserts et Douceurs',
                'dishes' => [
                    [
                        'name' => 'Baklawa aux Amandes',
                        'description' => 'Pâtisserie feuilletée aux amandes et miel, parfumée à l\'eau de fleur d\'oranger',
                        'price' => 250
                    ],
                    [
                        'name' => 'Makroudh aux Dattes',
                        'description' => 'Gâteau de semoule fourré aux dattes et parfumé à l\'eau de rose, nappé de miel',
                        'price' => 200
                    ],
                    [
                        'name' => 'Qalb el Louz',
                        'description' => 'Gâteau moelleux aux amandes et fleur d\'oranger, décoré de pistaches',
                        'price' => 300
                    ],
                    [
                        'name' => 'Charak aux Noix',
                        'description' => 'Pâtisserie traditionnelle aux noix et miel, saupoudrée de cannelle',
                        'price' => 220
                    ],
                    [
                        'name' => 'Mahalabia',
                        'description' => 'Crème dessert à la fleur d\'oranger, garnie de pistaches et amandes effilées',
                        'price' => 180
                    ]
                ]
            ],
            [
                'name' => 'Boissons Traditionnelles',
                'dishes' => [
                    [
                        'name' => 'Thé à la Menthe',
                        'description' => 'Thé vert traditionnel à la menthe fraîche, servi dans un verre à thé authentique',
                        'price' => 120
                    ],
                    [
                        'name' => 'Café Turc',
                        'description' => 'Café traditionnel préparé dans un cezve, servi avec loukoum',
                        'price' => 150
                    ],
                    [
                        'name' => 'Jus de Grenade Frais',
                        'description' => 'Jus de grenade fraîchement pressé, riche en antioxydants',
                        'price' => 200
                    ],
                    [
                        'name' => 'Lait de Poule aux Amandes',
                        'description' => 'Boisson crémeuse aux amandes et fleur d\'oranger, servie fraîche',
                        'price' => 180
                    ],
                    [
                        'name' => 'Limonade à la Menthe',
                        'description' => 'Limonade fraîche parfumée à la menthe et eau de rose',
                        'price' => 160
                    ]
                ]
            ]
        ];
        
        // Insérer les menus et leurs plats
        foreach ($menusData as $menuData) {
            // Insérer le menu
            $stmt = $conn->prepare("INSERT INTO Menu (name) VALUES (?)");
            $stmt->execute([$menuData['name']]);
            $menuId = $conn->lastInsertId();
            
            // Insérer les plats du menu
            foreach ($menuData['dishes'] as $dish) {
                $stmt = $conn->prepare("INSERT INTO Dish (name, description, price, menu_id) VALUES (?, ?, ?, ?)");
                $stmt->execute([$dish['name'], $dish['description'], $dish['price'], $menuId]);
            }
        }
        
        // Valider la transaction
        $conn->commit();
        
        return [
            "success" => true, 
            "message" => "Menu algérien inséré avec succès!",
            "details" => [
                "menus_count" => count($menusData),
                "total_dishes" => array_sum(array_map(function($menu) { return count($menu['dishes']); }, $menusData))
            ]
        ];
        
    } catch (PDOException $e) {
        if (isset($conn)) {
            $conn->rollBack();
        }
        return ["success" => false, "message" => "Erreur lors de l'insertion: " . $e->getMessage()];
    }
}

// Traitement de la requête
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';
    
    if ($action === 'insert_algerian_menu') {
        $result = insertAlgerianMenu();
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
} else {
    echo json_encode(["success" => false, "message" => "Méthode non autorisée."]);
}
?>
