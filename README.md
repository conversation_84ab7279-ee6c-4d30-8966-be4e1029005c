# Restaurant Reservation System / Système de Réservation de Restaurant

## Overview / Aperçu

A complete restaurant reservation management system built with HTML, CSS, JavaScript, PHP, and MySQL. This system provides a comprehensive solution for managing restaurant reservations, users, tables, and menus.

Un système complet de gestion des réservations de restaurant développé avec HTML, CSS, JavaScript, PHP et MySQL. Ce système fournit une solution complète pour gérer les réservations, les utilisateurs, les tables et les menus du restaurant.

## Features / Fonctionnalités

### ✅ User Management / Gestion des Utilisateurs
- User registration and authentication / Inscription et authentification des utilisateurs
- Role-based access control (Client, Manager, Admin) / Contrôle d'accès basé sur les rôles
- Profile management / Gestion des profils
- Password change functionality / Fonctionnalité de changement de mot de passe

### ✅ Reservation Management / Gestion des Réservations
- Table availability checking / Vérification de la disponibilité des tables
- Reservation creation, modification, and cancellation / Création, modification et annulation de réservations
- Reservation history and statistics / Historique et statistiques des réservations
- Date and time filtering / Filtrage par date et heure

### ✅ Table Management / Gestion des Tables
- Add, edit, and delete tables / Ajouter, modifier et supprimer des tables
- Table status management / Gestion du statut des tables
- Availability tracking / Suivi de la disponibilité

### ✅ Menu Management / Gestion des Menus
- Menu and dish management / Gestion des menus et des plats
- Complete CRUD operations / Opérations CRUD complètes
- Price and description management / Gestion des prix et descriptions

### ✅ Admin Dashboard / Tableau de Bord Administrateur
- Comprehensive admin panel / Panneau d'administration complet
- User role management / Gestion des rôles utilisateur
- System statistics and monitoring / Statistiques et surveillance du système

### ✅ Manager Dashboard / Tableau de Bord Manager
- Limited admin access for managers / Accès administrateur limité pour les managers
- Reservation and table management / Gestion des réservations et des tables

### ✅ Client Profile / Profil Client
- Personal information management / Gestion des informations personnelles
- Reservation history / Historique des réservations
- Personal statistics / Statistiques personnelles

## Technology Stack / Stack Technologique

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+ / MariaDB 10.2+
- **Architecture**: MVC Pattern / Modèle MVC

## Project Structure / Structure du Projet

```
projet/
├── backend/
│   ├── server/
│   │   ├── database.php          # Database configuration
│   │   ├── user.php             # User API
│   │   ├── reservation.php      # Reservation API
│   │   ├── table.php           # Table API
│   │   └── menu.php            # Menu API
│   ├── database.sql            # Database schema
│   └── test_connection.php     # Testing utilities
├── frontend/
│   ├── view/
│   │   ├── login/              # Login page
│   │   ├── signup/             # Registration page
│   │   ├── reservation/        # Reservation page
│   │   ├── admin/              # Admin dashboard
│   │   ├── manager/            # Manager dashboard
│   │   └── client/             # Client profile
│   ├── model/                  # JavaScript models
│   ├── controller/             # JavaScript controllers
│   ├── index.html              # Homepage
│   ├── style.css               # Main stylesheet
│   └── script.js               # Main JavaScript
├── test.html                   # Comprehensive testing page
├── تقرير_المشروع.md            # Arabic project report
├── description.md              # Project description
└── README.md                   # This file
```

## Installation / Installation

### Prerequisites / Prérequis
- Web server (Apache/Nginx) with PHP 7.4+ / Serveur web avec PHP 7.4+
- MySQL 5.7+ or MariaDB 10.2+ / MySQL 5.7+ ou MariaDB 10.2+
- PHP extensions: PDO, PDO_MySQL, session

### Setup Steps / Étapes d'Installation

1. **Clone or download the project / Cloner ou télécharger le projet**
   ```bash
   git clone [repository-url]
   cd projet
   ```

2. **Database setup / Configuration de la base de données**
   - Create a MySQL database / Créer une base de données MySQL
   - Import `backend/database.sql` / Importer `backend/database.sql`
   - Update database credentials in `backend/server/database.php`

3. **Configure database connection / Configurer la connexion à la base de données**
   ```php
   // Edit backend/server/database.php
   $host = 'localhost';
   $dbname = '4612192_restaurant'; // Your database name
   $username = 'your_username';
   $password = 'your_password';
   ```

4. **Upload backend files to server / Télécharger les fichiers backend sur le serveur**
   - Upload all files in `backend/server/` to `http://restaurantmila.atwebpages.com/sever/`
   - Ensure proper file permissions are set

5. **Set file permissions / Définir les permissions des fichiers**
   ```bash
   chmod 755 backend/server/
   chmod 644 backend/server/*.php
   ```

6. **Test the installation / Tester l'installation**
   - Open `test.html` in your browser / Ouvrir `test.html` dans votre navigateur
   - Run all tests to verify functionality / Exécuter tous les tests pour vérifier le fonctionnement

## Server Configuration / Configuration du Serveur

The application is configured to use the remote server:
**Backend URL**: `http://restaurantmila.atwebpages.com/sever/`

All API calls are now pointing to this server. Make sure to:
1. Upload all backend files to the server
2. Configure the database connection properly
3. Test the connection using the test page

## Usage / Utilisation

### Default Test Accounts / Comptes de Test par Défaut

After running the test data insertion, you can use these accounts:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | password123 |
| Manager | <EMAIL> | password123 |
| Client | <EMAIL> | password123 |
| Client | <EMAIL> | password123 |

### Getting Started / Commencer

1. **Access the homepage / Accéder à la page d'accueil**
   - Open `frontend/index.html` in your browser

2. **Register a new account / Créer un nouveau compte**
   - Click "S'inscrire" to create a new client account

3. **Login / Se connecter**
   - Use the test accounts or your newly created account

4. **Make a reservation / Faire une réservation**
   - Navigate to the reservation page
   - Select date, time, and number of guests
   - Choose an available table

5. **Admin access / Accès administrateur**
   - Login with admin credentials
   - Access the admin dashboard for full system management

## API Documentation / Documentation de l'API

### User API / API Utilisateur (`user.php`)
- `POST /register` - Register new user
- `POST /login` - User authentication
- `POST /logout` - User logout
- `GET /getUserInfo` - Get user information
- `POST /update_client` - Update client information
- `POST /change_password` - Change password

### Reservation API / API Réservation (`reservation.php`)
- `POST /check_availability` - Check table availability
- `POST /create_reservation` - Create new reservation
- `POST /get_client_reservations` - Get client reservations
- `POST /update_reservation` - Update reservation
- `POST /cancel_reservation` - Cancel reservation

### Table API / API Table (`table.php`)
- `GET /getAllTables` - Get all tables
- `POST /addTable` - Add new table
- `POST /updateTable` - Update table
- `POST /deleteTable` - Delete table

### Menu API / API Menu (`menu.php`)
- `GET /getAllMenus` - Get all menus
- `POST /addMenu` - Add new menu
- `POST /addDish` - Add new dish
- `POST /updateDish` - Update dish

## Testing / Tests

The system includes a comprehensive testing page (`test.html`) that verifies:
- Database connectivity / Connectivité de la base de données
- API functionality / Fonctionnalité de l'API
- Frontend page loading / Chargement des pages frontend
- User authentication / Authentification des utilisateurs
- Reservation operations / Opérations de réservation

## Security Features / Fonctionnalités de Sécurité

- Password hashing with PHP's `password_hash()` / Hachage des mots de passe
- SQL injection protection with prepared statements / Protection contre l'injection SQL
- Input validation and sanitization / Validation et assainissement des entrées
- Session management / Gestion des sessions
- Role-based access control / Contrôle d'accès basé sur les rôles

## Browser Support / Support des Navigateurs

- Chrome 60+ / Chrome 60+
- Firefox 55+ / Firefox 55+
- Safari 11+ / Safari 11+
- Edge 16+ / Edge 16+

## Contributing / Contribution

This is a complete educational project. For improvements or bug reports, please create an issue or submit a pull request.

## License / Licence

This project is for educational purposes. Feel free to use and modify as needed.

## Troubleshooting / Dépannage

### Common Issues / Problèmes Courants

1. **Database connection error / Erreur de connexion à la base de données**
   - Check database credentials in `backend/server/database.php`
   - Ensure MySQL service is running
   - Verify database exists and is accessible

2. **Login not working / Connexion ne fonctionne pas**
   - Check if test data is inserted
   - Verify password hashing is working
   - Check browser console for errors

3. **Reservations not loading / Réservations ne se chargent pas**
   - Ensure user is logged in
   - Check if tables exist in database
   - Verify API endpoints are accessible

4. **Client ID is null / ID client est null**
   - Use `frontend/debug_user.html` to check user data
   - Ensure client_id is returned from login API
   - Clear localStorage and login again if needed
   - See `CLIENT_ID_FIX_GUIDE.md` for detailed solution

5. **CORS errors / Erreurs CORS**
   - Ensure `.htaccess` file is uploaded to server
   - Check that CORS headers are properly set in PHP files
   - Verify server supports .htaccess files

## Support / Support

For questions or issues, please refer to:
- Project documentation in `تقرير_المشروع.md` (Arabic)
- Code comments (French)
- Test page for functionality verification
- `CLIENT_ID_FIX_GUIDE.md` for client ID issues
- `DEPLOYMENT_GUIDE.md` for deployment help

---

**Project Status: ✅ COMPLETE - Ready for production use**
**Statut du Projet: ✅ COMPLET - Prêt pour utilisation en production**
