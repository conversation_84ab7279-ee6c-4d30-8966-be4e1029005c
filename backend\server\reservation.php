<?php
// Headers CORS pour permettre les requêtes cross-origin
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Gérer les requêtes OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'database.php';

// Classe pour gérer les opérations liées aux réservations
class ReservationAPI {
    private $conn;

    public function __construct() {
        $this->conn = connectDB();
    }

    // Vérifier la disponibilité des tables
    public function checkAvailability($date, $time, $seats) {
        try {
            // Combiner la date et l'heure
            $dateTime = $date . ' ' . $time . ':00';

            // Trouver les tables qui ont le nombre de places requis ou plus
            $stmt = $this->conn->prepare(
                "SELECT id, seats, status FROM `Table`
                WHERE seats >= ? AND status = 'Available' AND id NOT IN (
                    SELECT table_id FROM Reservation
                    WHERE date_time BETWEEN DATE_SUB(?, INTERVAL 2 HOUR) AND DATE_ADD(?, INTERVAL 2 HOUR)
                    AND status != 'Cancelled'
                )
                ORDER BY seats ASC"
            );
            $stmt->execute([$seats, $dateTime, $dateTime]);

            $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ["success" => true, "tables" => $tables];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la vérification de disponibilité: " . $e->getMessage()];
        }
    }

    // Créer une nouvelle réservation
    public function createReservation($clientId, $tableId, $date, $time) {
        try {
            // Combiner la date et l'heure
            $dateTime = $date . ' ' . $time . ':00';

            // Vérifier si la table est disponible
            $availabilityCheck = $this->checkAvailability($date, $time, 1); // Le nombre de places n'est pas important ici

            if (!$availabilityCheck["success"]) {
                return $availabilityCheck; // Retourner l'erreur
            }

            $tableAvailable = false;
            foreach ($availabilityCheck["tables"] as $table) {
                if ($table["id"] == $tableId) {
                    $tableAvailable = true;
                    break;
                }
            }

            if (!$tableAvailable) {
                return ["success" => false, "message" => "Cette table n'est pas disponible à l'heure demandée."];
            }

            // Créer la réservation
            $stmt = $this->conn->prepare("INSERT INTO Reservation (date_time, status, client_id, table_id) VALUES (?, 'Confirmed', ?, ?)");
            $stmt->execute([$dateTime, $clientId, $tableId]);

            $reservationId = $this->conn->lastInsertId();

            return ["success" => true, "message" => "Réservation créée avec succès!", "reservation_id" => $reservationId];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la création de la réservation: " . $e->getMessage()];
        }
    }

    // Obtenir les détails d'une réservation
    public function getReservation($reservationId) {
        try {
            $stmt = $this->conn->prepare(
                "SELECT r.id, r.date_time, r.status, r.client_id, r.table_id, c.name as client_name, t.seats
                FROM Reservation r
                JOIN Client c ON r.client_id = c.id
                JOIN `Table` t ON r.table_id = t.id
                WHERE r.id = ?"
            );
            $stmt->execute([$reservationId]);

            if ($stmt->rowCount() == 0) {
                return ["success" => false, "message" => "Réservation non trouvée."];
            }

            $reservation = $stmt->fetch(PDO::FETCH_ASSOC);

            return ["success" => true, "reservation" => $reservation];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération de la réservation: " . $e->getMessage()];
        }
    }

    // Obtenir toutes les réservations d'un client
    public function getClientReservations($clientId) {
        try {
            $stmt = $this->conn->prepare(
                "SELECT r.id, r.date_time, r.status, r.table_id, t.seats
                FROM Reservation r
                JOIN `Table` t ON r.table_id = t.id
                WHERE r.client_id = ?
                ORDER BY r.date_time DESC"
            );
            $stmt->execute([$clientId]);

            $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ["success" => true, "reservations" => $reservations];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des réservations: " . $e->getMessage()];
        }
    }

    // Mettre à jour une réservation
    public function updateReservation($reservationId, $tableId, $date, $time, $status) {
        try {
            // Obtenir les informations actuelles de la réservation
            $currentReservation = $this->getReservation($reservationId);

            if (!$currentReservation["success"]) {
                return $currentReservation; // Retourner l'erreur
            }

            // Combiner la date et l'heure
            $dateTime = $date . ' ' . $time . ':00';

            // Si la table ou la date/heure a changé, vérifier la disponibilité
            if ($tableId != $currentReservation["reservation"]["table_id"] || $dateTime != $currentReservation["reservation"]["date_time"]) {
                // Vérifier si la nouvelle table est disponible
                $availabilityCheck = $this->checkAvailability($date, $time, 1); // Le nombre de places n'est pas important ici

                if (!$availabilityCheck["success"]) {
                    return $availabilityCheck; // Retourner l'erreur
                }

                $tableAvailable = false;
                foreach ($availabilityCheck["tables"] as $table) {
                    if ($table["id"] == $tableId) {
                        $tableAvailable = true;
                        break;
                    }
                }

                if (!$tableAvailable) {
                    return ["success" => false, "message" => "Cette table n'est pas disponible à l'heure demandée."];
                }
            }

            // Mettre à jour la réservation
            $stmt = $this->conn->prepare("UPDATE Reservation SET date_time = ?, status = ?, table_id = ? WHERE id = ?");
            $stmt->execute([$dateTime, $status, $tableId, $reservationId]);

            return ["success" => true, "message" => "Réservation mise à jour avec succès!"];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la mise à jour de la réservation: " . $e->getMessage()];
        }
    }

    // Annuler une réservation
    public function cancelReservation($reservationId) {
        try {
            $stmt = $this->conn->prepare("UPDATE Reservation SET status = 'Cancelled' WHERE id = ?");
            $stmt->execute([$reservationId]);

            return ["success" => true, "message" => "Réservation annulée avec succès!"];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de l'annulation de la réservation: " . $e->getMessage()];
        }
    }

    // Obtenir toutes les réservations (pour l'admin/manager)
    public function getAllReservations() {
        try {
            $stmt = $this->conn->prepare(
                "SELECT r.id, r.date_time, r.status, r.client_id, r.table_id, c.name as client_name, t.seats
                FROM Reservation r
                JOIN Client c ON r.client_id = c.id
                JOIN `Table` t ON r.table_id = t.id
                ORDER BY r.date_time DESC"
            );
            $stmt->execute();

            $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ["success" => true, "reservations" => $reservations];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des réservations: " . $e->getMessage()];
        }
    }

    // Obtenir les réservations pour une date spécifique (pour l'admin/manager)
    public function getReservationsByDate($date) {
        try {
            $stmt = $this->conn->prepare(
                "SELECT r.id, r.date_time, r.status, r.client_id, r.table_id, c.name as client_name, t.seats
                FROM Reservation r
                JOIN Client c ON r.client_id = c.id
                JOIN `Table` t ON r.table_id = t.id
                WHERE DATE(r.date_time) = ?
                ORDER BY r.date_time ASC"
            );
            $stmt->execute([$date]);

            $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ["success" => true, "reservations" => $reservations];

        } catch (PDOException $e) {
            return ["success" => false, "message" => "Erreur lors de la récupération des réservations: " . $e->getMessage()];
        }
    }
}

// Traitement des requêtes API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reservationAPI = new ReservationAPI();
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'check_availability':
            if (isset($data['date'], $data['time'], $data['seats'])) {
                $result = $reservationAPI->checkAvailability($data['date'], $data['time'], $data['seats']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour vérifier la disponibilité."]);
            }
            break;

        case 'create_reservation':
            if (isset($data['client_id'], $data['table_id'], $data['date'], $data['time'])) {
                $result = $reservationAPI->createReservation($data['client_id'], $data['table_id'], $data['date'], $data['time']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour créer la réservation."]);
            }
            break;

        case 'get_reservation':
            if (isset($data['reservation_id'])) {
                $result = $reservationAPI->getReservation($data['reservation_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de réservation manquant."]);
            }
            break;

        case 'get_client_reservations':
            if (isset($data['client_id'])) {
                $result = $reservationAPI->getClientReservations($data['client_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de client manquant."]);
            }
            break;

        case 'update_reservation':
            if (isset($data['reservation_id'], $data['table_id'], $data['date'], $data['time'], $data['status'])) {
                $result = $reservationAPI->updateReservation($data['reservation_id'], $data['table_id'], $data['date'], $data['time'], $data['status']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Données manquantes pour mettre à jour la réservation."]);
            }
            break;

        case 'cancel_reservation':
            if (isset($data['reservation_id'])) {
                $result = $reservationAPI->cancelReservation($data['reservation_id']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "ID de réservation manquant."]);
            }
            break;

        case 'get_all_reservations':
            $result = $reservationAPI->getAllReservations();
            echo json_encode($result);
            break;

        case 'get_reservations_by_date':
            if (isset($data['date'])) {
                $result = $reservationAPI->getReservationsByDate($data['date']);
                echo json_encode($result);
            } else {
                echo json_encode(["success" => false, "message" => "Date manquante."]);
            }
            break;

        default:
            echo json_encode(["success" => false, "message" => "Action non reconnue."]);
    }
}
?>