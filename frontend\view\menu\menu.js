/**
 * Script pour la gestion de l'affichage du menu
 */

// Variables globales
let currentUser = null;

// Initialisation de la page
document.addEventListener('DOMContentLoaded', function() {
    checkUserLoggedIn();
    loadMenus();
});

// Vérifier si l'utilisateur est connecté
function checkUserLoggedIn() {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
        window.location.href = '../login/login.html';
        return false;
    }
    currentUser = user;
    return true;
}

// Charger tous les menus
async function loadMenus() {
    showLoading();
    
    try {
        const result = await Menu.getAllMenus();
        
        if (result.success) {
            displayMenus(result.menus);
            showMenuContent();
        } else {
            showError(result.message);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des menus:', error);
        showError('Erreur de connexion au serveur');
    }
}

// Afficher les menus
function displayMenus(menus) {
    const container = document.getElementById('menus-container');
    container.innerHTML = '';
    
    if (!menus || menus.length === 0) {
        container.innerHTML = `
            <div class="no-menus">
                <h3>🍽️ Aucun menu disponible</h3>
                <p>Les menus seront bientôt disponibles. Revenez plus tard!</p>
            </div>
        `;
        return;
    }
    
    menus.forEach(menu => {
        const menuElement = createMenuElement(menu);
        container.appendChild(menuElement);
    });
}

// Créer un élément de menu
function createMenuElement(menu) {
    const menuDiv = document.createElement('div');
    menuDiv.className = 'menu-section';
    
    const dishesHtml = menu.dishes && menu.dishes.length > 0 
        ? menu.dishes.map(dish => createDishHtml(dish)).join('')
        : '<div class="no-dishes"><p>Aucun plat disponible dans ce menu</p></div>';
    
    menuDiv.innerHTML = `
        <div class="menu-title">
            <h3>${escapeHtml(menu.name)}</h3>
        </div>
        <div class="dishes-grid">
            ${dishesHtml}
        </div>
    `;
    
    return menuDiv;
}

// Créer le HTML d'un plat
function createDishHtml(dish) {
    return `
        <div class="dish-card">
            <div class="dish-name">${escapeHtml(dish.name)}</div>
            <div class="dish-description">${escapeHtml(dish.description)}</div>
            <div class="dish-price">${formatPrice(dish.price)} DA</div>
        </div>
    `;
}

// Formater le prix
function formatPrice(price) {
    return parseFloat(price).toLocaleString('fr-FR', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

// Échapper le HTML pour éviter les injections XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Afficher le loading
function showLoading() {
    document.getElementById('loading').classList.remove('hidden');
    document.getElementById('menu-content').classList.add('hidden');
    document.getElementById('error-message').classList.add('hidden');
}

// Afficher le contenu du menu
function showMenuContent() {
    document.getElementById('loading').classList.add('hidden');
    document.getElementById('menu-content').classList.remove('hidden');
    document.getElementById('error-message').classList.add('hidden');
}

// Afficher une erreur
function showError(message) {
    document.getElementById('loading').classList.add('hidden');
    document.getElementById('menu-content').classList.add('hidden');
    document.getElementById('error-message').classList.remove('hidden');
    document.getElementById('error-text').textContent = message;
}

// Navigation vers la page de réservation
function goToReservation() {
    window.location.href = '../reservation/reservation.html';
}

// Navigation vers le profil
function goToProfile() {
    if (currentUser.role === 'Admin') {
        window.location.href = '../admin/dashboard.html';
    } else if (currentUser.role === 'Manager') {
        window.location.href = '../manager/dashboard.html';
    } else {
        window.location.href = '../client/profile.html';
    }
}

// Déconnexion
async function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter?')) {
        try {
            await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=logout', { 
                method: 'POST' 
            });
        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
        }
        
        localStorage.removeItem('user');
        window.location.href = '../../index.html';
    }
}
