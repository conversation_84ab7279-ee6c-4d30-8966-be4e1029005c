-- Création de la base de données
USE 4612192_restaurant;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS User (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('Client', 'Manager', 'Admin') NOT NULL DEFAULT 'Client',
    status BOOLEAN NOT NULL DEFAULT TRUE
);

-- Table des clients
CREATE TABLE IF NOT EXISTS Client (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    user_id INT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE
);

-- Table des tables du restaurant
CREATE TABLE IF NOT EXISTS `Table` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    seats INT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'Available'
);

-- Table des réservations
CREATE TABLE IF NOT EXISTS Reservation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date_time DATETIME NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'Pending',
    client_id INT NOT NULL,
    table_id INT NOT NULL,
    FOREIGN KEY (client_id) REFERENCES Client(id) ON DELETE CASCADE,
    FOREIGN KEY (table_id) REFERENCES `Table`(id) ON DELETE CASCADE
);

-- Table des menus
CREATE TABLE IF NOT EXISTS Menu (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL
);

-- Table des plats
CREATE TABLE IF NOT EXISTS Dish (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    menu_id INT NOT NULL,
    FOREIGN KEY (menu_id) REFERENCES Menu(id) ON DELETE CASCADE
);

-- Table des paiements
CREATE TABLE IF NOT EXISTS Payment (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reservation_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    date DATETIME NOT NULL,
    method VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'Pending',
    FOREIGN KEY (reservation_id) REFERENCES Reservation(id) ON DELETE CASCADE
);

-- Insertion de données de test
-- Utilisateurs
INSERT INTO User (email, password, role, status) VALUES
('<EMAIL>', '$2y$10$abcdefghijklmnopqrstuuWVmDnVMQwAjNvWi6B9zLN6cxjrML2Kq', 'Admin', TRUE),
('<EMAIL>', '$2y$10$abcdefghijklmnopqrstuuWVmDnVMQwAjNvWi6B9zLN6cxjrML2Kq', 'Manager', TRUE),
('<EMAIL>', '$2y$10$abcdefghijklmnopqrstuuWVmDnVMQwAjNvWi6B9zLN6cxjrML2Kq', 'Client', TRUE),
('<EMAIL>', '$2y$10$abcdefghijklmnopqrstuuWVmDnVMQwAjNvWi6B9zLN6cxjrML2Kq', 'Client', TRUE);

-- Clients
INSERT INTO Client (name, phone, user_id) VALUES
('Jean Dupont', '0612345678', 3),
('Marie Martin', '0687654321', 4);

-- Tables
INSERT INTO `Table` (seats, status) VALUES
(2, 'Available'),
(2, 'Available'),
(4, 'Available'),
(4, 'Available'),
(6, 'Available'),
(8, 'Available');

-- Menus
INSERT INTO Menu (name) VALUES
('Menu du jour'),
('Menu dégustation'),
('Menu enfant');

-- Plats
INSERT INTO Dish (name, description, price, menu_id) VALUES
('Salade César', 'Salade romaine, croûtons, parmesan et sauce César', 8.50, 1),
('Steak frites', 'Steak de bœuf grillé avec frites maison', 15.90, 1),
('Tiramisu', 'Dessert italien au café et mascarpone', 6.50, 1),
('Foie gras', 'Foie gras de canard mi-cuit et son chutney de figues', 18.00, 2),
('Filet de bar', 'Filet de bar grillé, purée de patate douce et légumes de saison', 24.50, 2),
('Assiette de fromages', 'Sélection de fromages affinés', 9.00, 2),
('Nuggets poulet', 'Nuggets de poulet avec frites et ketchup', 7.90, 3),
('Glace deux boules', 'Deux boules de glace au choix', 4.50, 3);