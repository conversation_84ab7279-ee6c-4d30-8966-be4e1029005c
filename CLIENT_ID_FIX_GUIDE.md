# دليل إصلاح مشكلة Client ID

## المشكلة
المتغير `clientId` يبقى `null` في صفحة الحجوزات، مما يسبب مشاكل عند إنشاء الحجوزات.

## السبب
عدم تطابق في أسماء الحقول بين ما يرسله الخادم وما يتوقعه الكود في الواجهة الأمامية.

## الحلول المطبقة

### 1. تحديث دالة checkUserLoggedIn في reservation.js

```javascript
function checkUserLoggedIn() {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
        window.location.href = '../login/login.html';
        return false;
    }
    // استخدام fallback للحصول على client_id
    clientId = user.client_id || user.clientId || user.id;
    console.log('Client ID:', clientId, 'User data:', user);
    return true;
}
```

### 2. إضافة تحقق إضافي في دالة إنشاء الحجز

```javascript
if (!clientId) {
    showMessage(confirmationMessage, 'Erreur: ID client non trouvé. Veuillez vous reconnecter.', 'error');
    return;
}
```

### 3. تحديث دالة loadReservations في profile.js

```javascript
// استخدام client_id مع fallback
const clientId = currentUser.client_id || currentUser.clientId || currentUser.id;
console.log('Loading reservations for client ID:', clientId);
```

## أدوات التشخيص

### 1. صفحة Debug User Data
تم إنشاء `frontend/debug_user.html` للتحقق من:
- بيانات المستخدم المحفوظة في localStorage
- وجود client_id
- اختبار تسجيل الدخول

### 2. رسائل Console للتشخيص
تم إضافة `console.log` في النقاط الحرجة لتتبع:
- قيم client_id
- بيانات المستخدم الكاملة
- حالة الطلبات

## كيفية الاختبار

### 1. فتح صفحة Debug
```
http://your-domain/frontend/debug_user.html
```

### 2. التحقق من البيانات
- انقر على "Vérifier les données"
- تحقق من وجود client_id
- إذا لم يكن موجود، انقر على "Test Login"

### 3. اختبار الحجوزات
1. سجل الدخول كعميل
2. اذهب إلى صفحة الحجوزات
3. تحقق من console للرسائل التشخيصية
4. جرب إنشاء حجز جديد

## البيانات المتوقعة من الخادم

عند تسجيل الدخول، يجب أن يرجع الخادم:

```json
{
    "success": true,
    "message": "Connexion réussie!",
    "user": {
        "id": 1,
        "role": "Client",
        "client_id": 1,
        "name": "اسم العميل"
    }
}
```

## حسابات الاختبار

للاختبار، استخدم:
- **Email**: <EMAIL>
- **Password**: password123

## إذا استمرت المشكلة

### 1. تحقق من قاعدة البيانات
```sql
SELECT User.id, User.email, User.role, Client.id as client_id, Client.name
FROM User
LEFT JOIN Client ON User.id = Client.user_id
WHERE User.email = '<EMAIL>';
```

### 2. تحقق من ملف user.php
تأكد من أن دالة login ترجع client_id:

```php
return [
    "success" => true,
    "message" => "Connexion réussie!",
    "user" => [
        "id" => $user['id'],
        "role" => $user['role'],
        "client_id" => $user['client_id'] ?? null,
        "name" => $user['name'] ?? null
    ]
];
```

### 3. مسح localStorage وإعادة تسجيل الدخول
```javascript
localStorage.clear();
// ثم سجل الدخول مرة أخرى
```

## الملفات المحدثة

- ✅ `frontend/view/reservation/reservation.js`
- ✅ `frontend/view/client/profile.js`
- ✅ `frontend/debug_user.html` (جديد)
- ✅ `CLIENT_ID_FIX_GUIDE.md` (جديد)

## ملاحظات مهمة

1. **Fallback Strategy**: الكود يحاول عدة حقول للحصول على client_id
2. **Error Handling**: رسائل خطأ واضحة عند فقدان client_id
3. **Debug Tools**: أدوات تشخيص لتسهيل حل المشاكل
4. **Console Logging**: رسائل تشخيصية في console المتصفح

## الخطوات التالية

1. اختبر النظام باستخدام debug_user.html
2. تأكد من أن client_id يظهر في البيانات
3. جرب إنشاء حجز جديد
4. إذا استمرت المشكلة، تحقق من قاعدة البيانات والخادم
