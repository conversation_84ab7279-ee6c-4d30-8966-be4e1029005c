<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réservation - Système de Réservation de Restaurant</title>
    <link rel="stylesheet" href="reservation.css">
</head>
<body>
    <div class="container">
        <div class="reservation-container">
            <h1>Réservation de Table</h1>
            
            <!-- Formulaire de vérification de disponibilité -->
            <div id="availability-section" class="section">
                <h2>Vérifier la disponibilité</h2>
                <form id="availability-form">
                    <div class="form-group">
                        <label for="date">Date</label>
                        <input type="date" id="date" name="date" required min="">
                    </div>
                    <div class="form-group">
                        <label for="time">Heure</label>
                        <select id="time" name="time" required>
                            <option value="">Sélectionnez une heure</option>
                            <option value="12:00">12:00</option>
                            <option value="12:30">12:30</option>
                            <option value="13:00">13:00</option>
                            <option value="13:30">13:30</option>
                            <option value="19:00">19:00</option>
                            <option value="19:30">19:30</option>
                            <option value="20:00">20:00</option>
                            <option value="20:30">20:30</option>
                            <option value="21:00">21:00</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="seats">Nombre de personnes</label>
                        <select id="seats" name="seats" required>
                            <option value="">Sélectionnez le nombre</option>
                            <option value="1">1 personne</option>
                            <option value="2">2 personnes</option>
                            <option value="3">3 personnes</option>
                            <option value="4">4 personnes</option>
                            <option value="5">5 personnes</option>
                            <option value="6">6 personnes</option>
                            <option value="7">7 personnes</option>
                            <option value="8">8 personnes</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn-primary">Vérifier la disponibilité</button>
                    </div>
                </form>
                <div id="availability-message" class="message"></div>
            </div>
            
            <!-- Section des tables disponibles -->
            <div id="tables-section" class="section hidden">
                <h2>Tables disponibles</h2>
                <div id="tables-list" class="tables-grid"></div>
                <div class="form-group">
                    <button id="back-to-availability" class="btn-secondary">Retour</button>
                </div>
            </div>
            
            <!-- Section de confirmation de réservation -->
            <div id="confirmation-section" class="section hidden">
                <h2>Confirmation de réservation</h2>
                <div id="reservation-details" class="details-box">
                    <p><strong>Date:</strong> <span id="confirm-date"></span></p>
                    <p><strong>Heure:</strong> <span id="confirm-time"></span></p>
                    <p><strong>Nombre de personnes:</strong> <span id="confirm-seats"></span></p>
                    <p><strong>Table:</strong> <span id="confirm-table"></span></p>
                </div>
                <div class="form-group">
                    <button id="confirm-reservation" class="btn-primary">Confirmer la réservation</button>
                    <button id="back-to-tables" class="btn-secondary">Retour</button>
                </div>
                <div id="confirmation-message" class="message"></div>
            </div>
            
            <!-- Section de gestion des réservations -->
            <div id="manage-reservations-section" class="section hidden">
                <h2>Mes réservations</h2>
                <div id="reservations-list"></div>
                <div class="form-group">
                    <button id="back-to-main" class="btn-secondary">Nouvelle réservation</button>
                </div>
            </div>
        </div>
        
        <div class="nav-buttons">
            <a href="../../index.html" class="btn-secondary">Retour à l'accueil</a>
            <button id="view-reservations" class="btn-primary">Voir mes réservations</button>
        </div>
    </div>
    
    <!-- Modèle pour la modification de réservation -->
    <div id="edit-modal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Modifier la réservation</h2>
            <form id="edit-form">
                <input type="hidden" id="edit-reservation-id">
                <div class="form-group">
                    <label for="edit-date">Date</label>
                    <input type="date" id="edit-date" name="edit-date" required>
                </div>
                <div class="form-group">
                    <label for="edit-time">Heure</label>
                    <select id="edit-time" name="edit-time" required>
                        <option value="12:00">12:00</option>
                        <option value="12:30">12:30</option>
                        <option value="13:00">13:00</option>
                        <option value="13:30">13:30</option>
                        <option value="19:00">19:00</option>
                        <option value="19:30">19:30</option>
                        <option value="20:00">20:00</option>
                        <option value="20:30">20:30</option>
                        <option value="21:00">21:00</option>
                    </select>
                </div>
                <div id="edit-tables-list" class="tables-grid"></div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Enregistrer les modifications</button>
                </div>
            </form>
            <div id="edit-message" class="message"></div>
        </div>
    </div>
    
    <!-- Inclusion des scripts -->
    <script src="../../model/User.js"></script>
    <script src="../../model/Table.js"></script>
    <script src="../../model/Reservation.js"></script>
    <script src="../../controller/reservationController.js"></script>
    <script src="reservation.js"></script>
</body>
</html>