<?php
// Test de connexion à la base de données et vérification des tables

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Gérer les requêtes OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'server/database.php';

function testDatabaseConnection() {
    try {
        $conn = connectDB();
        return ["success" => true, "message" => "Connexion à la base de données réussie"];
    } catch (Exception $e) {
        return ["success" => false, "message" => "Erreur de connexion: " . $e->getMessage()];
    }
}

function testTables() {
    try {
        $conn = connectDB();

        $tables = ['User', 'Client', 'Table', 'Reservation', 'Menu', 'Dish', 'Payment'];
        $results = [];

        foreach ($tables as $table) {
            try {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM `$table`");
                $stmt->execute();
                $count = $stmt->fetchColumn();
                $results[$table] = ["exists" => true, "count" => $count];
            } catch (PDOException $e) {
                $results[$table] = ["exists" => false, "error" => $e->getMessage()];
            }
        }

        return ["success" => true, "tables" => $results];
    } catch (Exception $e) {
        return ["success" => false, "message" => "Erreur lors de la vérification des tables: " . $e->getMessage()];
    }
}

function testSampleData() {
    try {
        $conn = connectDB();

        // Vérifier s'il y a des données de test
        $stmt = $conn->prepare("SELECT COUNT(*) FROM User");
        $stmt->execute();
        $userCount = $stmt->fetchColumn();

        $stmt = $conn->prepare("SELECT COUNT(*) FROM `Table`");
        $stmt->execute();
        $tableCount = $stmt->fetchColumn();

        $stmt = $conn->prepare("SELECT COUNT(*) FROM Menu");
        $stmt->execute();
        $menuCount = $stmt->fetchColumn();

        return [
            "success" => true,
            "data" => [
                "users" => $userCount,
                "tables" => $tableCount,
                "menus" => $menuCount
            ]
        ];
    } catch (Exception $e) {
        return ["success" => false, "message" => "Erreur lors de la vérification des données: " . $e->getMessage()];
    }
}

function insertSampleData() {
    try {
        $conn = connectDB();

        // Vérifier si les données existent déjà
        $stmt = $conn->prepare("SELECT COUNT(*) FROM User");
        $stmt->execute();
        $userCount = $stmt->fetchColumn();

        if ($userCount > 0) {
            return ["success" => true, "message" => "Les données de test existent déjà"];
        }

        // Insérer des utilisateurs de test
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);

        $users = [
            ['<EMAIL>', $hashedPassword, 'Admin', 1],
            ['<EMAIL>', $hashedPassword, 'Manager', 1],
            ['<EMAIL>', $hashedPassword, 'Client', 1],
            ['<EMAIL>', $hashedPassword, 'Client', 1]
        ];

        foreach ($users as $user) {
            $stmt = $conn->prepare("INSERT INTO User (email, password, role, status) VALUES (?, ?, ?, ?)");
            $stmt->execute($user);
        }

        // Insérer des clients
        $clients = [
            ['Jean Dupont', '0612345678', 3],
            ['Marie Martin', '0687654321', 4]
        ];

        foreach ($clients as $client) {
            $stmt = $conn->prepare("INSERT INTO Client (name, phone, user_id) VALUES (?, ?, ?)");
            $stmt->execute($client);
        }

        // Insérer des tables
        $tables = [
            [2, 'Available'],
            [2, 'Available'],
            [4, 'Available'],
            [4, 'Available'],
            [6, 'Available'],
            [8, 'Available']
        ];

        foreach ($tables as $table) {
            $stmt = $conn->prepare("INSERT INTO `Table` (seats, status) VALUES (?, ?)");
            $stmt->execute($table);
        }

        // Insérer des menus
        $menus = [
            ['Menu du jour'],
            ['Menu dégustation'],
            ['Menu enfant']
        ];

        foreach ($menus as $menu) {
            $stmt = $conn->prepare("INSERT INTO Menu (name) VALUES (?)");
            $stmt->execute($menu);
        }

        // Insérer des plats
        $dishes = [
            ['Salade César', 'Salade romaine, croûtons, parmesan et sauce César', 8.50, 1],
            ['Steak frites', 'Steak de bœuf grillé avec frites maison', 15.90, 1],
            ['Tiramisu', 'Dessert italien au café et mascarpone', 6.50, 1],
            ['Foie gras', 'Foie gras de canard mi-cuit et son chutney de figues', 18.00, 2],
            ['Filet de bar', 'Filet de bar grillé, purée de patate douce et légumes de saison', 24.50, 2],
            ['Assiette de fromages', 'Sélection de fromages affinés', 9.00, 2],
            ['Nuggets poulet', 'Nuggets de poulet avec frites et ketchup', 7.90, 3],
            ['Glace deux boules', 'Deux boules de glace au choix', 4.50, 3]
        ];

        foreach ($dishes as $dish) {
            $stmt = $conn->prepare("INSERT INTO Dish (name, description, price, menu_id) VALUES (?, ?, ?, ?)");
            $stmt->execute($dish);
        }

        return ["success" => true, "message" => "Données de test insérées avec succès"];

    } catch (Exception $e) {
        return ["success" => false, "message" => "Erreur lors de l'insertion des données: " . $e->getMessage()];
    }
}

// Traitement des requêtes
$action = $_GET['action'] ?? 'test_connection';

switch ($action) {
    case 'test_connection':
        echo json_encode(testDatabaseConnection());
        break;

    case 'test_tables':
        echo json_encode(testTables());
        break;

    case 'test_data':
        echo json_encode(testSampleData());
        break;

    case 'insert_data':
        echo json_encode(insertSampleData());
        break;

    case 'full_test':
        $results = [
            'connection' => testDatabaseConnection(),
            'tables' => testTables(),
            'data' => testSampleData()
        ];
        echo json_encode($results);
        break;

    default:
        echo json_encode(["success" => false, "message" => "Action non reconnue"]);
}
?>
