/* Styles pour la page de réservation */

/* Styles généraux */
body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

/* Styles pour le conteneur de réservation */
.reservation-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 20px;
}

/* Styles pour les sections */
.section {
    margin-bottom: 30px;
}

.hidden {
    display: none;
}

/* Styles pour les formulaires */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

input[type="date"],
select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

/* Styles pour les boutons */
button, .btn-primary, .btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

/* Styles pour les messages */
.message {
    padding: 10px;
    margin-top: 10px;
    border-radius: 4px;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Styles pour la grille de tables */
.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.table-card {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.table-card.selected {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* Styles pour la boîte de détails */
.details-box {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

/* Styles pour les boutons de navigation */
.nav-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

/* Styles pour la liste des réservations */
#reservations-list {
    margin-bottom: 20px;
}

.reservation-item {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.reservation-item .actions {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

/* Styles pour la modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
    display: block;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

/* Responsive design */
@media (max-width: 768px) {
    .tables-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .modal-content {
        margin: 20% auto;
        width: 90%;
    }
    
    .nav-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .nav-buttons a, .nav-buttons button {
        width: 100%;
        text-align: center;
    }
}