````markdown
# Web Project 

## 1. Project Overview
This project aims to build a full-stack web application for restaurant reservation management . Users can register, log in, view table availability, make and manage reservations, while administrators can oversee tables, menu items, and user accounts.

## 2. Relational Model
- **User**: (id, email, password, role, status)
- **Client**: (id, name, phone, user_id)
- **Reservation**: (id, date_time, status, client_id, table_id)
- **Table**: (id, seats, status)
- **Menu**: (id, name)
- **Dish**: (id, name, description, price, menu_id)
- **Payment**: (id, reservation_id, amount, date, method, status)

## 3. Classes
```uml
class User {
  +id: int
  +email: string
  +password: string
  +role: enum{Client, Manager, Admin}
  +status: boolean
}
class Client {
  +id: int
  +name: string
  +phone: string
  +user_id: int
}
class Reservation {
  +id: int
  +date_time: DateTime
  +status: string
  +client_id: int
  +table_id: int
}
class Table {
  +id: int
  +seats: int
  +status: string
}
class Menu {
  +id: int
  +name: string
}
class Dish {
  +id: int
  +name: string
  +description: string
  +price: decimal
  +menu_id: int
}
class Payment {
  +id: int
  +reservation_id: int
  +amount: decimal
  +date: DateTime
  +method: string
  +status: string
}
````

## 4. Architecture and Folder Structure

This project follows the MVC (Model-View-Controller) architecture, with two main root folders:

```
project-root/
├── frontend/
│   ├── model/
│   │   └── (contains class definitions)
│   ├── view/
│   │   ├── signup/         
│   │   │   ├── signup.html
│   │   │   ├── signup.css
│   │   │   └── signup.js
│   │   ├── login/
│   │   │   ├── login.html
│   │   │   └── login.js
│   │   └── ... (other use-case folders)
│   └── controller/
│       ├── signupController.js
│       ├── loginController.js
│       └── ... (other business logic controllers)

├── backend/
│   ├── database.sql        
│   └── server/
│       ├── database.php    (database connection)
│       ├── user.php        (User-related API)
│       └── reservation.php (Reservation-related API)

```

## 5. Use Case Scenarios. Use Case Scenarios

### 5.1 Sign Up

1. User provides email, password, and name.
2. System checks for existing account with the same email.
3. Account is created and a confirmation is sent.

### 5.2 Sign In

1. User enters email and password.
2. System verifies credentials.
3. User is redirected to their dashboard based on role.

### 5.3 View Table Availability

1. Client selects date, time, and party size.
2. System queries available tables.
3. Available tables are displayed.

### 5.4 Create Reservation

1. Client chooses a table from availability.
2. System records the reservation and sends confirmation.

### 5.5 Edit Reservation

1. Client selects an existing reservation.
2. Client modifies date/time or table.
3. System checks availability and updates the reservation.

### 5.6 Cancel Reservation

1. Client selects a reservation to cancel.
2. System removes the reservation and confirms the cancellation.

## 6. Additional Commands
* this application will be hosted later on awardspace so make that in count to change the url easly .
* The official language of the application — for all documentation, source code, and user interface — is **French**.
* Use only the following core technologies:

  * **HTML**
  * **CSS**
  * **JavaScript**
  * **PHP**
  * **MySQL**
