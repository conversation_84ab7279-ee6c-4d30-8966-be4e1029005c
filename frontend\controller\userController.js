/**
 * Contrôleur pour gérer les opérations liées aux utilisateurs
 */
class UserController {
    /**
     * G<PERSON> l'inscription d'un nouvel utilisateur
     * @param {string} name - Le nom complet de l'utilisateur
     * @param {string} email - L'adresse email de l'utilisateur
     * @param {string} phone - Le numéro de téléphone de l'utilisateur
     * @param {string} password - Le mot de passe de l'utilisateur
     * @param {string} confirmPassword - La confirmation du mot de passe
     * @returns {Promise} - Résultat de l'inscription
     */
    static async register(name, email, phone, password, confirmPassword) {
        try {
            // Validation des entrées
            if (!name || !email || !phone || !password || !confirmPassword) {
                return { success: false, message: 'Veuillez remplir tous les champs.' };
            }
            
            // Vérification de la correspondance des mots de passe
            if (password !== confirmPassword) {
                return { success: false, message: 'Les mots de passe ne correspondent pas.' };
            }
            
            // Validation de l'email avec une expression régulière
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return { success: false, message: 'Veuillez entrer une adresse email valide.' };
            }
            
            // Validation du numéro de téléphone
            const phoneRegex = /^\d{10}$/;
            if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
                return { success: false, message: 'Veuillez entrer un numéro de téléphone valide (10 chiffres).' };
            }
            
            // Validation du mot de passe (au moins 8 caractères)
            if (password.length < 8) {
                return { success: false, message: 'Le mot de passe doit contenir au moins 8 caractères.' };
            }
            
            // Appel au modèle pour l'inscription
            const result = await User.register(name, email, phone, password);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de l\'inscription.' };
        }
    }
    
    /**
     * Gère la connexion d'un utilisateur
     * @param {string} email - L'adresse email de l'utilisateur
     * @param {string} password - Le mot de passe de l'utilisateur
     * @returns {Promise} - Résultat de la connexion
     */
    static async login(email, password) {
        try {
            // Validation des entrées
            if (!email || !password) {
                return { success: false, message: 'Veuillez remplir tous les champs.' };
            }
            
            // Validation de l'email avec une expression régulière
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return { success: false, message: 'Veuillez entrer une adresse email valide.' };
            }
            
            // Appel au modèle pour la connexion
            const result = await User.login(email, password);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la connexion.' };
        }
    }
    
    /**
     * Gère la déconnexion d'un utilisateur
     * @returns {Promise} - Résultat de la déconnexion
     */
    static async logout() {
        try {
            // Suppression des informations utilisateur du localStorage
            localStorage.removeItem('user');
            
            // Appel au modèle pour la déconnexion
            const result = await User.logout();
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la déconnexion.' };
        }
    }
    
    /**
     * Récupère les informations d'un utilisateur
     * @param {number} userId - L'ID de l'utilisateur
     * @returns {Promise} - Informations de l'utilisateur
     */
    static async getUserInfo(userId) {
        try {
            if (!userId) {
                return { success: false, message: 'ID utilisateur non spécifié.' };
            }
            
            const result = await User.getUserInfo(userId);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la récupération des informations utilisateur.' };
        }
    }
    
    /**
     * Met à jour les informations d'un client
     * @param {number} clientId - L'ID du client
     * @param {string} name - Le nouveau nom
     * @param {string} phone - Le nouveau numéro de téléphone
     * @returns {Promise} - Résultat de la mise à jour
     */
    static async updateClientInfo(clientId, name, phone) {
        try {
            // Validation des entrées
            if (!clientId || !name || !phone) {
                return { success: false, message: 'Informations incomplètes.' };
            }
            
            // Validation du numéro de téléphone
            const phoneRegex = /^\d{10}$/;
            if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
                return { success: false, message: 'Veuillez entrer un numéro de téléphone valide (10 chiffres).' };
            }
            
            // Appel au modèle pour mettre à jour les informations
            const result = await User.updateClientInfo(clientId, name, phone);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors de la mise à jour des informations.' };
        }
    }
    
    /**
     * Change le mot de passe d'un utilisateur
     * @param {number} userId - L'ID de l'utilisateur
     * @param {string} currentPassword - Le mot de passe actuel
     * @param {string} newPassword - Le nouveau mot de passe
     * @param {string} confirmPassword - La confirmation du nouveau mot de passe
     * @returns {Promise} - Résultat du changement de mot de passe
     */
    static async changePassword(userId, currentPassword, newPassword, confirmPassword) {
        try {
            // Validation des entrées
            if (!userId || !currentPassword || !newPassword || !confirmPassword) {
                return { success: false, message: 'Veuillez remplir tous les champs.' };
            }
            
            // Vérification de la correspondance des mots de passe
            if (newPassword !== confirmPassword) {
                return { success: false, message: 'Les nouveaux mots de passe ne correspondent pas.' };
            }
            
            // Validation du mot de passe (au moins 8 caractères)
            if (newPassword.length < 8) {
                return { success: false, message: 'Le mot de passe doit contenir au moins 8 caractères.' };
            }
            
            // Appel au modèle pour changer le mot de passe
            const result = await User.changePassword(userId, currentPassword, newPassword);
            return result;
        } catch (error) {
            console.error('Erreur dans le contrôleur:', error);
            return { success: false, message: 'Une erreur est survenue lors du changement de mot de passe.' };
        }
    }
}