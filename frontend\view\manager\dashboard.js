/**
 * Script pour le tableau de bord manager
 */

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier l'authentification et les permissions
    checkManagerAuth();

    // Initialiser la navigation
    initializeNavigation();

    // Charger les données du tableau de bord
    loadDashboardData();

    // Initialiser les gestionnaires d'événements
    initializeEventHandlers();
});

/**
 * Vérifie l'authentification et les permissions manager
 */
function checkManagerAuth() {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user || (user.role !== 'Manager' && user.role !== 'Admin')) {
        alert('Accès non autorisé. Redirection vers la page de connexion.');
        window.location.href = '../login/login.html';
        return;
    }

    console.log('Utilisateur manager connecté:', user);
}

/**
 * Initialise la navigation entre les sections
 */
function initializeNavigation() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    const sections = document.querySelectorAll('.section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);

            // Masquer toutes les sections
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Afficher la section cible
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Mettre à jour la navigation active
            navLinks.forEach(navLink => {
                navLink.classList.remove('active');
            });
            this.classList.add('active');

            // Charger les données spécifiques à la section
            loadSectionData(targetId);
        });
    });
}

/**
 * Charge les données du tableau de bord
 */
async function loadDashboardData() {
    try {
        // Charger les statistiques
        await loadStats();

        // Charger les réservations du jour
        await loadTodayReservations();

    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showMessage('Erreur lors du chargement des données du tableau de bord.', 'error');
    }
}

/**
 * Charge les statistiques
 */
async function loadStats() {
    try {
        // Charger le nombre de réservations aujourd'hui
        const today = new Date().toISOString().split('T')[0];
        const reservationsResponse = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_reservations_by_date', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date: today })
        });
        const reservationsData = await reservationsResponse.json();

        if (reservationsData.success) {
            document.getElementById('total-reservations').textContent = reservationsData.reservations.length;
        }

        // Charger le nombre de tables
        const tablesResponse = await fetch('http://restaurantmila.atwebpages.com/sever/table.php?action=getAllTables');
        const tablesData = await tablesResponse.json();

        if (tablesData.success) {
            const availableTables = tablesData.tables.filter(table => table.status === 'Available');
            document.getElementById('total-tables').textContent = availableTables.length;

            // Calculer le taux d'occupation
            const totalTables = tablesData.tables.length;
            const occupiedTables = totalTables - availableTables.length;
            const occupationRate = totalTables > 0 ? Math.round((occupiedTables / totalTables) * 100) : 0;
            document.getElementById('occupation-rate').textContent = occupationRate + '%';
        }

        // Charger le nombre de clients (utilisateurs avec rôle Client)
        const usersResponse = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=get_all_users', {
            method: 'POST'
        });
        const usersData = await usersResponse.json();

        if (usersData.success) {
            const clients = usersData.users.filter(user => user.role === 'Client' && user.status);
            document.getElementById('total-clients').textContent = clients.length;
        }

    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
    }
}

/**
 * Charge les réservations du jour
 */
async function loadTodayReservations() {
    try {
        const today = new Date().toISOString().split('T')[0];
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_reservations_by_date', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date: today })
        });
        const data = await response.json();

        if (data.success) {
            displayTodayReservations(data.reservations);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des réservations du jour:', error);
    }
}

/**
 * Affiche les réservations du jour
 */
function displayTodayReservations(reservations) {
    const container = document.getElementById('today-reservations');

    if (reservations.length === 0) {
        container.innerHTML = '<p>Aucune réservation pour aujourd\'hui.</p>';
        return;
    }

    container.innerHTML = reservations.map(reservation => {
        const date = new Date(reservation.date_time);
        const time = date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });

        return `
            <div class="activity-item">
                <div class="activity-time">${time}</div>
                <div class="activity-message">
                    ${reservation.client_name} - Table ${reservation.table_id} (${reservation.seats} places)
                    <span class="status-badge status-${reservation.status.toLowerCase()}">${reservation.status}</span>
                </div>
            </div>
        `;
    }).join('');
}

/**
 * Charge les données spécifiques à une section
 */
function loadSectionData(sectionId) {
    switch (sectionId) {
        case 'reservations':
            loadReservations();
            break;
        case 'tables':
            loadTables();
            break;
        case 'menu':
            loadMenu();
            break;
    }
}

/**
 * Charge la liste des réservations
 */
async function loadReservations() {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_all_reservations', {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            displayReservations(data.reservations);
        } else {
            showMessage('Erreur lors du chargement des réservations: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des réservations.', 'error');
    }
}

/**
 * Affiche la liste des réservations
 */
function displayReservations(reservations) {
    const tbody = document.getElementById('reservations-tbody');

    tbody.innerHTML = reservations.map(reservation => {
        const date = new Date(reservation.date_time);
        const formattedDate = date.toLocaleDateString('fr-FR');
        const formattedTime = date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });

        return `
            <tr>
                <td>${reservation.id}</td>
                <td>${reservation.client_name}</td>
                <td>${formattedDate} ${formattedTime}</td>
                <td>Table ${reservation.table_id}</td>
                <td>${reservation.seats}</td>
                <td>
                    <span class="status-badge status-${reservation.status.toLowerCase()}">
                        ${reservation.status}
                    </span>
                </td>
                <td>
                    <button class="btn-warning" onclick="changeReservationStatus(${reservation.id}, '${reservation.status}')">Statut</button>
                    ${reservation.status !== 'Cancelled' ?
                        `<button class="btn-danger" onclick="cancelReservation(${reservation.id})">Annuler</button>` :
                        ''
                    }
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Charge la liste des tables
 */
async function loadTables() {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/table.php?action=getAllTables');
        const data = await response.json();

        if (data.success) {
            displayTables(data.tables);
        } else {
            showMessage('Erreur lors du chargement des tables: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des tables.', 'error');
    }
}

/**
 * Affiche la liste des tables
 */
function displayTables(tables) {
    const tbody = document.getElementById('tables-tbody');

    tbody.innerHTML = tables.map(table => `
        <tr>
            <td>${table.id}</td>
            <td>${table.seats}</td>
            <td>
                <span class="status-badge status-${table.status.toLowerCase()}">
                    ${table.status}
                </span>
            </td>
            <td>
                <button class="btn-warning" onclick="editTable(${table.id}, ${table.seats}, '${table.status}')">Modifier</button>
            </td>
        </tr>
    `).join('');
}

/**
 * Initialise les gestionnaires d'événements
 */
function initializeEventHandlers() {
    // Déconnexion
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });

    // Formulaire de table
    document.getElementById('table-form').addEventListener('submit', handleTableSubmit);

    // Formulaire de statut de réservation
    document.getElementById('reservation-status-form').addEventListener('submit', handleReservationStatusSubmit);
}

/**
 * Gère la déconnexion
 */
async function logout() {
    try {
        localStorage.removeItem('user');
        await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=logout', { method: 'POST' });
        window.location.href = '../../index.html';
    } catch (error) {
        console.error('Erreur lors de la déconnexion:', error);
        window.location.href = '../../index.html';
    }
}

/**
 * Affiche un message à l'utilisateur
 */
function showMessage(message, type) {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';

    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

// Fonctions utilitaires pour les actions
function refreshReservations() { loadReservations(); }
function refreshTables() { loadTables(); }
function refreshMenu() { loadMenu(); }

function filterReservationsByDate() {
    const selectedDate = document.getElementById('reservation-date').value;
    if (selectedDate) {
        // Filtrer les réservations par date
        loadReservationsByDate(selectedDate);
    } else {
        loadReservations();
    }
}

async function loadReservationsByDate(date) {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_reservations_by_date', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date })
        });
        const data = await response.json();

        if (data.success) {
            displayReservations(data.reservations);
        }
    } catch (error) {
        console.error('Erreur:', error);
    }
}

function filterReservationsByStatus() {
    // Implémentation du filtrage par statut
    loadReservations(); // Pour l'instant, recharger toutes les réservations
}

function showAddTableModal() {
    document.getElementById('table-modal-title').textContent = 'Ajouter une table';
    document.getElementById('table-form').reset();
    document.getElementById('table-id').value = '';
    document.getElementById('table-modal').style.display = 'block';
}

function editTable(id, seats, status) {
    document.getElementById('table-modal-title').textContent = 'Modifier la table';
    document.getElementById('table-id').value = id;
    document.getElementById('table-seats').value = seats;
    document.getElementById('table-status').value = status;
    document.getElementById('table-modal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

async function handleTableSubmit(e) {
    e.preventDefault();

    const id = document.getElementById('table-id').value;
    const seats = document.getElementById('table-seats').value;
    const status = document.getElementById('table-status').value;

    const action = id ? 'updateTable' : 'addTable';
    const data = id ? { table_id: id, seats, status } : { seats, status };

    try {
        const response = await fetch(`http://restaurantmila.atwebpages.com/sever/table.php?action=${action}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            closeModal('table-modal');
            loadTables();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors de l\'opération.', 'error');
    }
}

function changeReservationStatus(reservationId, currentStatus) {
    document.getElementById('reservation-status-id').value = reservationId;
    document.getElementById('reservation-status-select').value = currentStatus;
    document.getElementById('reservation-status-modal').style.display = 'block';
}

async function handleReservationStatusSubmit(e) {
    e.preventDefault();

    const reservationId = document.getElementById('reservation-status-id').value;
    const newStatus = document.getElementById('reservation-status-select').value;

    try {
        // Pour modifier le statut, nous devons d'abord récupérer les détails de la réservation
        const getResponse = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_reservation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ reservation_id: reservationId })
        });

        const reservationData = await getResponse.json();

        if (reservationData.success) {
            const reservation = reservationData.reservation;
            const date = new Date(reservation.date_time);
            const formattedDate = date.toISOString().split('T')[0];
            const formattedTime = date.toTimeString().split(' ')[0].substring(0, 5);

            const updateResponse = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=update_reservation', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    reservation_id: reservationId,
                    table_id: reservation.table_id,
                    date: formattedDate,
                    time: formattedTime,
                    status: newStatus
                })
            });

            const result = await updateResponse.json();

            if (result.success) {
                showMessage('Statut de la réservation modifié avec succès!', 'success');
                closeModal('reservation-status-modal');
                loadReservations();
                loadDashboardData(); // Recharger les stats
            } else {
                showMessage(result.message, 'error');
            }
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors de la modification du statut.', 'error');
    }
}

async function cancelReservation(reservationId) {
    if (!confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) return;

    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=cancel_reservation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ reservation_id: reservationId })
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            loadReservations();
            loadDashboardData(); // Recharger les stats
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors de l\'annulation.', 'error');
    }
}

function loadMenu() {
    // Implémentation du chargement du menu
    document.getElementById('menu-content').innerHTML = '<p>Fonctionnalité de gestion du menu en cours de développement.</p>';
}
