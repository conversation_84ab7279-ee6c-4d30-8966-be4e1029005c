/**
 * Script pour le tableau de bord administrateur
 */

// Variable globale pour stocker la liste complète des utilisateurs
let allUsers = [];

document.addEventListener('DOMContentLoaded', function() {
    // Vérifier l'authentification et les permissions
    checkAdminAuth();

    // Initialiser la navigation
    initializeNavigation();

    // Charger les données du tableau de bord
    loadDashboardData();

    // Initialiser les gestionnaires d'événements
    initializeEventHandlers();
});

/**
 * Vérifie l'authentification et les permissions admin
 */
function checkAdminAuth() {
    const user = JSON.parse(localStorage.getItem('user'));

    if (!user || user.role !== 'Admin') {
        alert('Accès non autorisé. Redirection vers la page de connexion.');
        window.location.href = '../login/login.html';
        return;
    }

    console.log('Utilisateur admin connecté:', user);
}

/**
 * Initialise la navigation entre les sections
 */
function initializeNavigation() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    const sections = document.querySelectorAll('.section');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);

            // Masquer toutes les sections
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Afficher la section cible
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Mettre à jour la navigation active
            navLinks.forEach(navLink => {
                navLink.classList.remove('active');
            });
            this.classList.add('active');

            // Charger les données spécifiques à la section
            loadSectionData(targetId);
        });
    });
}

/**
 * Charge les données du tableau de bord
 */
async function loadDashboardData() {
    try {
        // Charger les statistiques
        await loadStats();

        // Charger l'activité récente
        await loadRecentActivity();

        // Charger les utilisateurs
        await loadUsers();

    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showMessage('Erreur lors du chargement des données du tableau de bord.', 'error');
    }
}

/**
 * Charge les statistiques
 */
async function loadStats() {
    try {
        // Charger le nombre total d'utilisateurs
        const usersResponse = await fetch('../../backend/server/user.php?action=get_all_users', {
            method: 'POST'
        });
        const usersData = await usersResponse.json();

        if (usersData.success) {
            const users = usersData.users;

            // Total des utilisateurs
            document.getElementById('total-users').textContent = users.length;

            // Compter par rôle
            const admins = users.filter(user => user.role === 'Admin');
            const managers = users.filter(user => user.role === 'Manager');
            const clients = users.filter(user => user.role === 'Client');

            document.getElementById('total-admins').textContent = admins.length;
            document.getElementById('total-managers').textContent = managers.length;
            document.getElementById('total-clients').textContent = clients.length;
        }

    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
    }
}

/**
 * Charge l'activité récente
 */
async function loadRecentActivity() {
    const activityList = document.getElementById('recent-activity-list');

    // Simuler des activités récentes (à remplacer par de vraies données)
    const activities = [
        {
            time: '10:30',
            message: 'Nouvelle réservation créée par Jean Dupont'
        },
        {
            time: '09:15',
            message: 'Utilisateur Marie Martin inscrit'
        },
        {
            time: '08:45',
            message: 'Table 5 mise en maintenance'
        }
    ];

    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-time">${activity.time}</div>
            <div class="activity-message">${activity.message}</div>
        </div>
    `).join('');
}

/**
 * Charge les données spécifiques à une section
 */
function loadSectionData(sectionId) {
    switch (sectionId) {
        case 'users':
            loadUsers();
            break;
        case 'reservations':
            loadReservations();
            break;
        case 'tables':
            loadTables();
            break;
        case 'menu':
            loadMenu();
            break;
    }
}

/**
 * Charge la liste des utilisateurs
 */
async function loadUsers() {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=get_all_users', {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            allUsers = data.users; // Sauvegarder la liste complète
            displayUsers(data.users);
        } else {
            showMessage('Erreur lors du chargement des utilisateurs: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des utilisateurs.', 'error');
    }
}

/**
 * Affiche la liste des utilisateurs
 */
function displayUsers(users) {
    const tbody = document.getElementById('users-tbody');
    const searchTerm = document.getElementById('user-search').value.toLowerCase().trim();

    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.id}</td>
            <td>${highlightSearchTerm(user.email, searchTerm)}</td>
            <td>${highlightSearchTerm(user.name || 'N/A', searchTerm)}</td>
            <td>${highlightSearchTerm(user.role, searchTerm)}</td>
            <td>
                <span class="status-badge ${user.status ? 'status-active' : 'status-inactive'}">
                    ${user.status ? 'Actif' : 'Inactif'}
                </span>
            </td>
            <td>
                <button class="btn-warning" onclick="changeUserRole(${user.id}, '${user.role}')">Rôle</button>
                <button class="btn-${user.status ? 'danger' : 'success'}" onclick="toggleUserStatus(${user.id}, ${!user.status})">
                    ${user.status ? 'Désactiver' : 'Activer'}
                </button>
            </td>
        </tr>
    `).join('');

    // Mettre à jour le compteur si c'est l'affichage initial
    if (searchTerm === '') {
        updateSearchResultsCount(users.length, users.length, '');
    }
}

/**
 * Met en surbrillance le terme de recherche dans le texte
 */
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm || !text) return text;

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<span class="search-highlight">$1</span>');
}

/**
 * Charge la liste des réservations
 */
async function loadReservations() {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/reservation.php?action=get_all_reservations', {
            method: 'POST'
        });
        const data = await response.json();

        if (data.success) {
            displayReservations(data.reservations);
        } else {
            showMessage('Erreur lors du chargement des réservations: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des réservations.', 'error');
    }
}

/**
 * Affiche la liste des réservations
 */
function displayReservations(reservations) {
    const tbody = document.getElementById('reservations-tbody');

    tbody.innerHTML = reservations.map(reservation => {
        const date = new Date(reservation.date_time);
        const formattedDate = date.toLocaleDateString('fr-FR');
        const formattedTime = date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });

        return `
            <tr>
                <td>${reservation.id}</td>
                <td>${reservation.client_name}</td>
                <td>${formattedDate} ${formattedTime}</td>
                <td>Table ${reservation.table_id}</td>
                <td>${reservation.seats}</td>
                <td>
                    <span class="status-badge status-${reservation.status.toLowerCase()}">
                        ${reservation.status}
                    </span>
                </td>
                <td>
                    <button class="btn-warning" onclick="editReservation(${reservation.id})">Modifier</button>
                    <button class="btn-danger" onclick="cancelReservation(${reservation.id})">Annuler</button>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Charge la liste des tables
 */
async function loadTables() {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/table.php?action=getAllTables');
        const data = await response.json();

        if (data.success) {
            displayTables(data.tables);
        } else {
            showMessage('Erreur lors du chargement des tables: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des tables.', 'error');
    }
}

/**
 * Affiche la liste des tables
 */
function displayTables(tables) {
    const tbody = document.getElementById('tables-tbody');

    tbody.innerHTML = tables.map(table => `
        <tr>
            <td>${table.id}</td>
            <td>${table.seats}</td>
            <td>
                <span class="status-badge status-${table.status.toLowerCase()}">
                    ${table.status}
                </span>
            </td>
            <td>
                <button class="btn-warning" onclick="editTable(${table.id}, ${table.seats}, '${table.status}')">Modifier</button>
                <button class="btn-danger" onclick="deleteTable(${table.id})">Supprimer</button>
            </td>
        </tr>
    `).join('');
}

/**
 * Initialise les gestionnaires d'événements
 */
function initializeEventHandlers() {
    // Déconnexion
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });

    // Formulaire de rôle utilisateur
    document.getElementById('user-role-form').addEventListener('submit', handleUserRoleSubmit);
}

/**
 * Gère la déconnexion
 */
async function logout() {
    try {
        localStorage.removeItem('user');
        await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=logout', { method: 'POST' });
        window.location.href = '../../index.html';
    } catch (error) {
        console.error('Erreur lors de la déconnexion:', error);
        window.location.href = '../../index.html';
    }
}

/**
 * Affiche un message à l'utilisateur
 */
function showMessage(message, type) {
    const messageDiv = document.getElementById('message');
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';

    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

// Fonctions utilitaires pour les actions
function refreshUsers() { loadUsers(); }

/**
 * Filtre les utilisateurs selon le terme de recherche
 */
function filterUsers() {
    const searchTerm = document.getElementById('user-search').value.toLowerCase().trim();

    if (searchTerm === '') {
        // Si le champ de recherche est vide, afficher tous les utilisateurs
        displayUsers(allUsers);
        return;
    }

    // Filtrer les utilisateurs selon plusieurs critères
    const filteredUsers = allUsers.filter(user => {
        const name = (user.name || '').toLowerCase();
        const email = (user.email || '').toLowerCase();
        const role = (user.role || '').toLowerCase();
        const id = user.id.toString();

        // Recherche dans le nom, email, rôle et ID
        return name.includes(searchTerm) ||
               email.includes(searchTerm) ||
               role.includes(searchTerm) ||
               id.includes(searchTerm);
    });

    // Afficher les résultats filtrés
    displayUsers(filteredUsers);

    // Mettre à jour le compteur de résultats
    updateSearchResultsCount(filteredUsers.length, allUsers.length, searchTerm);

    // Afficher un message si aucun résultat
    if (filteredUsers.length === 0) {
        const tbody = document.getElementById('users-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                    <i>Aucun utilisateur trouvé pour "${searchTerm}"</i>
                </td>
            </tr>
        `;
    }
}

/**
 * Efface la recherche et affiche tous les utilisateurs
 */
function clearSearch() {
    document.getElementById('user-search').value = '';
    displayUsers(allUsers);
    updateSearchResultsCount(allUsers.length, allUsers.length, '');
}

/**
 * Met à jour l'affichage du nombre de résultats
 */
function updateSearchResultsCount(filteredCount, totalCount, searchTerm) {
    const countElement = document.getElementById('search-results-count');

    if (searchTerm === '') {
        countElement.textContent = `${totalCount} utilisateur(s) au total`;
    } else {
        countElement.textContent = `${filteredCount} résultat(s) sur ${totalCount} utilisateur(s)`;
    }
}

/**
 * Gère les événements clavier dans le champ de recherche
 */
function handleSearchKeyup(event) {
    // Appeler la fonction de filtrage
    filterUsers();

    // Gestion des touches spéciales
    switch(event.key) {
        case 'Escape':
            clearSearch();
            break;
        case 'Enter':
            // Optionnel: action spéciale sur Enter
            const searchTerm = event.target.value.trim();
            if (searchTerm) {
                console.log(`Recherche effectuée: "${searchTerm}"`);
            }
            break;
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function changeUserRole(userId, currentRole) {
    document.getElementById('user-role-id').value = userId;
    document.getElementById('user-role-select').value = currentRole;
    document.getElementById('user-role-modal').style.display = 'block';
}

async function handleUserRoleSubmit(e) {
    e.preventDefault();

    const userId = document.getElementById('user-role-id').value;
    const newRole = document.getElementById('user-role-select').value;

    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=change_user_role', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_id: userId, role: newRole })
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            closeModal('user-role-modal');
            loadUsers();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors de la modification du rôle.', 'error');
    }
}

async function toggleUserStatus(userId, newStatus) {
    try {
        const response = await fetch('http://restaurantmila.atwebpages.com/sever/user.php?action=change_user_status', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_id: userId, status: newStatus })
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            loadUsers();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors de la modification du statut.', 'error');
    }
}





